<?php
/**
 * Sistema de Diagnóstico Inteligente para Arquivos Não Reconhecidos
 * 
 * Analisa arquivos que não foram reconhecidos pelos padrões existentes
 * e fornece informações detalhadas para implementação de novos padrões
 */

// Verifica se o arquivo foi enviado
if (!isset($_GET['arquivo']) || empty($_GET['arquivo'])) {
    die("Arquivo não especificado");
}

$arquivo = $_GET['arquivo'];
if (!file_exists($arquivo)) {
    die("Arquivo não encontrado: $arquivo");
}

echo "<!DOCTYPE html>";
echo "<html><head><title>Diagnóstico de Arquivo - IN-1888</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
.container { max-width: 1400px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
.ok { color: green; font-weight: bold; }
.error { color: red; font-weight: bold; }
.warning { color: orange; font-weight: bold; }
.info { color: blue; font-weight: bold; }
.section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
.preview { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px; }
.analysis { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0; }
.suggestion { background: #fff3e0; padding: 15px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #ff9800; }
.pattern-match { background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 5px 0; }
table { width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 12px; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; font-weight: bold; }
.score { padding: 3px 8px; border-radius: 3px; color: white; font-weight: bold; }
.score-high { background: #4caf50; }
.score-medium { background: #ff9800; }
.score-low { background: #f44336; }
.btn { display: inline-block; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; color: white; font-weight: bold; }
.btn-primary { background: #2196f3; }
.btn-success { background: #4caf50; }
.btn-warning { background: #ff9800; }
</style></head><body>";

echo "<div class='container'>";
echo "<h1>🔍 Diagnóstico de Arquivo Não Reconhecido</h1>";

$nomeArquivo = basename($arquivo);
$tamanhoArquivo = filesize($arquivo);
$extensao = pathinfo($arquivo, PATHINFO_EXTENSION);

echo "<div class='section'>";
echo "<h2>📄 Informações do Arquivo</h2>";
echo "<p><strong>Nome:</strong> $nomeArquivo</p>";
echo "<p><strong>Tamanho:</strong> " . number_format($tamanhoArquivo) . " bytes</p>";
echo "<p><strong>Extensão:</strong> $extensao</p>";
echo "<p><strong>Caminho:</strong> $arquivo</p>";
echo "</div>";

// Lê as primeiras linhas do arquivo
$linhas = [];
$handle = fopen($arquivo, 'r');
if ($handle) {
    for ($i = 0; $i < 10 && ($linha = fgets($handle)) !== false; $i++) {
        $linhas[] = trim($linha);
    }
    fclose($handle);
}

if (empty($linhas)) {
    echo "<span class='error'>❌ Não foi possível ler o arquivo</span>";
    echo "</div></body></html>";
    exit;
}

// Análise da primeira linha
echo "<div class='section'>";
echo "<h2>🔍 Análise da Primeira Linha</h2>";

$primeiraLinha = $linhas[0];
$campos = str_getcsv($primeiraLinha);
$numeroCampos = count($campos);

echo "<div class='preview'>";
echo "<strong>Primeira linha:</strong><br>";
echo htmlspecialchars($primeiraLinha);
echo "</div>";

echo "<p><strong>Número de campos:</strong> $numeroCampos</p>";

// Detecta se é cabeçalho ou dados
$ehCabecalho = detectarSeCabecalho($campos);

echo "<div class='analysis'>";
if ($ehCabecalho) {
    echo "<span class='ok'>✅ Primeira linha parece ser CABEÇALHO</span>";
} else {
    echo "<span class='warning'>⚠️ Primeira linha parece ser DADOS (sem cabeçalho)</span>";
}
echo "</div>";

// Mostra campos analisados
echo "<h3>📋 Campos Detectados:</h3>";
echo "<table>";
echo "<tr><th>Posição</th><th>Valor</th><th>Tipo Detectado</th><th>Observações</th></tr>";

foreach ($campos as $index => $campo) {
    $tipo = detectarTipoCampo($campo);
    $observacoes = analisarCampo($campo);
    
    echo "<tr>";
    echo "<td>" . ($index + 1) . "</td>";
    echo "<td>" . htmlspecialchars($campo) . "</td>";
    echo "<td>$tipo</td>";
    echo "<td>$observacoes</td>";
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// Preview de mais linhas
echo "<div class='section'>";
echo "<h2>👁️ Preview do Arquivo (Primeiras 10 Linhas)</h2>";
echo "<div class='preview'>";
foreach ($linhas as $index => $linha) {
    $numero = $index + 1;
    echo "<strong>Linha $numero:</strong> " . htmlspecialchars($linha) . "<br>";
}
echo "</div>";
echo "</div>";

// Análise de padrões existentes
echo "<div class='section'>";
echo "<h2>🎯 Comparação com Padrões Existentes</h2>";

$padroes = obterPadroesExistentes();
$scores = [];

foreach ($padroes as $nomePadrao => $padrao) {
    $score = calcularSimilaridade($campos, $padrao['campos']);
    $scores[$nomePadrao] = $score;
}

// Ordena por score
arsort($scores);

echo "<table>";
echo "<tr><th>Padrão</th><th>Score de Similaridade</th><th>Campos Compatíveis</th><th>Status</th></tr>";

foreach ($scores as $nomePadrao => $score) {
    $scoreClass = $score >= 70 ? 'score-high' : ($score >= 40 ? 'score-medium' : 'score-low');
    $status = $score >= 70 ? 'Muito Similar' : ($score >= 40 ? 'Parcialmente Similar' : 'Pouco Similar');
    
    $camposCompativeis = array_intersect($campos, $padroes[$nomePadrao]['campos']);
    
    echo "<tr>";
    echo "<td><strong>$nomePadrao</strong></td>";
    echo "<td><span class='score $scoreClass'>{$score}%</span></td>";
    echo "<td>" . implode(', ', $camposCompativeis) . "</td>";
    echo "<td>$status</td>";
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// Sugestões baseadas na análise
echo "<div class='section'>";
echo "<h2>💡 Sugestões de Ação</h2>";

$melhorScore = max($scores);
$melhorPadrao = array_key_first($scores);

if ($melhorScore >= 70) {
    echo "<div class='suggestion'>";
    echo "<h3>🎯 Sugestão Principal: Adaptar Padrão Existente</h3>";
    echo "<p>O arquivo tem <strong>{$melhorScore}% de similaridade</strong> com o padrão <strong>$melhorPadrao</strong>.</p>";
    echo "<p><strong>Ação recomendada:</strong> Modificar o padrão existente para incluir este formato.</p>";
    echo "</div>";
} elseif ($melhorScore >= 40) {
    echo "<div class='suggestion'>";
    echo "<h3>⚙️ Sugestão: Criar Variação do Padrão</h3>";
    echo "<p>O arquivo tem <strong>{$melhorScore}% de similaridade</strong> com o padrão <strong>$melhorPadrao</strong>.</p>";
    echo "<p><strong>Ação recomendada:</strong> Criar uma variação do padrão existente.</p>";
    echo "</div>";
} else {
    echo "<div class='suggestion'>";
    echo "<h3>🆕 Sugestão: Criar Novo Padrão</h3>";
    echo "<p>O arquivo tem baixa similaridade com padrões existentes (melhor: <strong>{$melhorScore}%</strong>).</p>";
    echo "<p><strong>Ação recomendada:</strong> Implementar um novo padrão específico.</p>";
    echo "</div>";
}

// Análise específica do exemplo
if (!$ehCabecalho && $numeroCampos == 6) {
    echo "<div class='suggestion'>";
    echo "<h3>🔍 Análise Específica do Seu Arquivo</h3>";
    echo "<p>Baseado na estrutura detectada, este parece ser um <strong>extrato de transações da Binance</strong> sem cabeçalho.</p>";
    echo "<p><strong>Estrutura provável:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Campo 1:</strong> Data/Hora (2025-06-30 22:54:40)</li>";
    echo "<li><strong>Campo 2:</strong> Tipo de Conta (Spot Market Account)</li>";
    echo "<li><strong>Campo 3:</strong> Operação (p2p_add)</li>";
    echo "<li><strong>Campo 4:</strong> Moeda (USDT)</li>";
    echo "<li><strong>Campo 5:</strong> Quantidade (+355.********)</li>";
    echo "<li><strong>Campo 6:</strong> Saldo/Valor (811.********)</li>";
    echo "</ul>";
    echo "<p><strong>Recomendação:</strong> Criar padrão 'binance_extrato_sem_cabecalho'</p>";
    echo "</div>";
}

// Código sugerido para implementação
echo "<h3>💻 Código Sugerido para Implementação</h3>";
echo "<div class='preview'>";
echo htmlspecialchars("
// Adicionar ao switch de padrões
case 'binance_extrato_sem_cabecalho':
    \$indices = [
        'data_hora' => 0,
        'tipo_conta' => 1,
        'operacao' => 2,
        'moeda' => 3,
        'quantidade' => 4,
        'saldo' => 5
    ];
    
    // Processar sem pular primeira linha (não há cabeçalho)
    rewind(\$handle);
    while ((\$linha = fgetcsv(\$handle)) !== false) {
        if (count(\$linha) >= 6) {
            \$dados[] = [
                'data' => \$linha[0],
                'tipo' => \$linha[2], // operacao
                'moeda' => \$linha[3],
                'quantidade' => \$linha[4],
                'valor' => \$linha[5]
            ];
        }
    }
    break;
");
echo "</div>";

echo "</div>";

// Botões de ação
echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='salvar_padrao_nao_reconhecido.php?arquivo=" . urlencode($arquivo) . "' class='btn btn-warning'>💾 Salvar para Análise</a>";
echo "<a href='index.php' class='btn btn-primary'>🏠 Voltar ao Sistema</a>";
echo "<a href='implementar_padrao.php?arquivo=" . urlencode($arquivo) . "' class='btn btn-success'>⚙️ Implementar Padrão</a>";
echo "</div>";

echo "</div>";
echo "</body></html>";

// Funções auxiliares
function detectarSeCabecalho($campos) {
    $indicadoresCabecalho = ['data', 'date', 'time', 'valor', 'value', 'amount', 'quantity', 'symbol', 'pair', 'type', 'side'];
    $indicadoresDados = ['/^\d{4}-\d{2}-\d{2}/', '/^\d{2}\/\d{2}\/\d{4}/', '/^[+-]?\d+\.?\d*$/', '/^[A-Z]{3,4}$/'];
    
    $scoreCabecalho = 0;
    $scoreDados = 0;
    
    foreach ($campos as $campo) {
        $campoLower = strtolower(trim($campo));
        
        // Verifica indicadores de cabeçalho
        foreach ($indicadoresCabecalho as $indicador) {
            if (strpos($campoLower, $indicador) !== false) {
                $scoreCabecalho++;
                break;
            }
        }
        
        // Verifica indicadores de dados
        foreach ($indicadoresDados as $pattern) {
            if (preg_match($pattern, trim($campo))) {
                $scoreDados++;
                break;
            }
        }
    }
    
    return $scoreCabecalho > $scoreDados;
}

function detectarTipoCampo($campo) {
    $campo = trim($campo);
    
    if (preg_match('/^\d{4}-\d{2}-\d{2}/', $campo)) return 'Data ISO';
    if (preg_match('/^\d{2}\/\d{2}\/\d{4}/', $campo)) return 'Data BR';
    if (preg_match('/^[+-]?\d+\.?\d*$/', $campo)) return 'Numérico';
    if (preg_match('/^[A-Z]{3,4}$/', $campo)) return 'Símbolo Cripto';
    if (preg_match('/^[a-z_]+$/', strtolower($campo))) return 'Identificador';
    if (strlen($campo) > 20) return 'Texto Longo';
    
    return 'Texto';
}

function analisarCampo($campo) {
    $observacoes = [];
    
    if (strpos($campo, '+') === 0 || strpos($campo, '-') === 0) {
        $observacoes[] = 'Sinal de débito/crédito';
    }
    
    if (preg_match('/\d{2}:\d{2}:\d{2}/', $campo)) {
        $observacoes[] = 'Contém horário';
    }
    
    if (in_array(strtoupper($campo), ['USDT', 'BTC', 'ETH', 'BNB', 'BUSD'])) {
        $observacoes[] = 'Criptomoeda conhecida';
    }
    
    return empty($observacoes) ? '-' : implode(', ', $observacoes);
}

function obterPadroesExistentes() {
    return [
        'binance_geral' => [
            'campos' => ['Date(UTC)', 'Pair', 'Side', 'Order Amount', 'Order Price', 'Fee', 'Realized PNL']
        ],
        'binance_p2p' => [
            'campos' => ['Order Number', 'Order Type', 'Asset', 'Fiat', 'Total Price', 'Price', 'Quantity', 'Counterparty', 'Status', 'Created Time']
        ],
        'gate_deposits' => [
            'campos' => ['Time', 'Currency', 'Amount', 'Address', 'TXID', 'Status']
        ]
    ];
}

function calcularSimilaridade($campos1, $campos2) {
    $intersection = array_intersect(array_map('strtolower', $campos1), array_map('strtolower', $campos2));
    $union = array_unique(array_merge(array_map('strtolower', $campos1), array_map('strtolower', $campos2)));
    
    return count($union) > 0 ? round((count($intersection) / count($union)) * 100) : 0;
}
?>
