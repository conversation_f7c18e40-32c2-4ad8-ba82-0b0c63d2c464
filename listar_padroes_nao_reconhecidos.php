<?php
/**
 * Lista todos os padrões não reconhecidos salvos para análise
 */

echo "<!DOCTYPE html>";
echo "<html><head><title><PERSON><PERSON><PERSON><PERSON> Não Reconhecidos - IN-1888</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
.container { max-width: 1400px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
.ok { color: green; font-weight: bold; }
.error { color: red; font-weight: bold; }
.warning { color: orange; font-weight: bold; }
.info { color: blue; font-weight: bold; }
.section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
table { width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 12px; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; font-weight: bold; }
.btn { display: inline-block; padding: 8px 16px; margin: 2px; text-decoration: none; border-radius: 4px; color: white; font-size: 11px; }
.btn-primary { background: #2196f3; }
.btn-success { background: #4caf50; }
.btn-warning { background: #ff9800; }
.btn-danger { background: #f44336; }
.stats { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0; }
.preview { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; font-size: 11px; max-height: 100px; overflow-y: auto; }
</style></head><body>";

echo "<div class='container'>";
echo "<h1>📋 Padrões Não Reconhecidos</h1>";

$diretorio = 'padroes_nao_reconhecidos';

if (!is_dir($diretorio)) {
    echo "<div class='section'>";
    echo "<span class='info'>ℹ️ Nenhum padrão não reconhecido foi salvo ainda.</span>";
    echo "<p>Quando o sistema encontrar arquivos com padrões desconhecidos, eles serão automaticamente salvos aqui para análise.</p>";
    echo "</div>";
    
    echo "<div style='text-align: center; margin-top: 30px;'>";
    echo "<a href='index.php' class='btn btn-primary'>🏠 Voltar ao Sistema</a>";
    echo "</div>";
    
    echo "</div></body></html>";
    exit;
}

// Lista arquivos no diretório
$arquivos = glob($diretorio . '/*');
$arquivosCSV = array_filter($arquivos, function($arquivo) {
    return pathinfo($arquivo, PATHINFO_EXTENSION) === 'csv' || pathinfo($arquivo, PATHINFO_EXTENSION) === 'xlsx';
});

$relatóriosJSON = array_filter($arquivos, function($arquivo) {
    return pathinfo($arquivo, PATHINFO_EXTENSION) === 'json';
});

// Estatísticas
echo "<div class='stats'>";
echo "<h2>📊 Estatísticas</h2>";
echo "<p><strong>Total de arquivos salvos:</strong> " . count($arquivosCSV) . "</p>";
echo "<p><strong>Relatórios de análise:</strong> " . count($relatóriosJSON) . "</p>";
echo "<p><strong>Diretório:</strong> $diretorio</p>";

// Lê log se existir
$logFile = $diretorio . '/log_arquivos_nao_reconhecidos.txt';
if (file_exists($logFile)) {
    $linhasLog = file($logFile, FILE_IGNORE_NEW_LINES);
    echo "<p><strong>Entradas no log:</strong> " . count($linhasLog) . "</p>";
}
echo "</div>";

if (empty($arquivosCSV)) {
    echo "<div class='section'>";
    echo "<span class='info'>ℹ️ Nenhum arquivo não reconhecido encontrado.</span>";
    echo "</div>";
} else {
    echo "<div class='section'>";
    echo "<h2>📁 Arquivos Não Reconhecidos</h2>";
    
    echo "<table>";
    echo "<tr><th>Data/Hora</th><th>Nome do Arquivo</th><th>Tamanho</th><th>Primeira Linha (Preview)</th><th>Ações</th></tr>";
    
    // Ordena arquivos por data (mais recente primeiro)
    usort($arquivosCSV, function($a, $b) {
        return filemtime($b) - filemtime($a);
    });
    
    foreach ($arquivosCSV as $arquivo) {
        $nomeArquivo = basename($arquivo);
        $tamanho = filesize($arquivo);
        $dataModificacao = date('d/m/Y H:i:s', filemtime($arquivo));
        
        // Lê primeira linha para preview
        $primeiraLinha = '';
        $handle = fopen($arquivo, 'r');
        if ($handle) {
            $primeiraLinha = fgets($handle);
            fclose($handle);
        }
        
        // Procura relatório JSON correspondente
        $timestamp = substr($nomeArquivo, 0, 19); // Extrai timestamp do nome
        $relatorioJSON = $diretorio . '/' . $timestamp . '_relatorio.json';
        $temRelatorio = file_exists($relatorioJSON);
        
        echo "<tr>";
        echo "<td>$dataModificacao</td>";
        echo "<td><strong>$nomeArquivo</strong></td>";
        echo "<td>" . number_format($tamanho) . " bytes</td>";
        echo "<td><div class='preview'>" . htmlspecialchars(substr($primeiraLinha, 0, 100)) . "</div></td>";
        echo "<td>";
        
        echo "<a href='diagnostico_arquivos.php?arquivo=" . urlencode($arquivo) . "' class='btn btn-primary'>🔍 Diagnosticar</a>";
        
        if ($temRelatorio) {
            echo "<a href='ver_relatorio.php?arquivo=" . urlencode($relatorioJSON) . "' class='btn btn-success'>📊 Relatório</a>";
        }
        
        echo "<a href='implementar_padrao.php?arquivo=" . urlencode($arquivo) . "' class='btn btn-warning'>⚙️ Implementar</a>";
        echo "<a href='download.php?arquivo=" . urlencode($arquivo) . "' class='btn btn-primary'>💾 Download</a>";
        
        echo "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    echo "</div>";
}

// Mostra log se existir
if (file_exists($logFile)) {
    echo "<div class='section'>";
    echo "<h2>📝 Log de Arquivos Não Reconhecidos</h2>";
    
    $linhasLog = file($logFile, FILE_IGNORE_NEW_LINES);
    $linhasRecentes = array_slice(array_reverse($linhasLog), 0, 20); // Últimas 20 entradas
    
    echo "<div class='preview' style='max-height: 300px;'>";
    foreach ($linhasRecentes as $linha) {
        echo htmlspecialchars($linha) . "\n";
    }
    echo "</div>";
    
    if (count($linhasLog) > 20) {
        echo "<p><em>Mostrando as 20 entradas mais recentes de " . count($linhasLog) . " total.</em></p>";
    }
    echo "</div>";
}

// Análise de padrões comuns
if (!empty($relatóriosJSON)) {
    echo "<div class='section'>";
    echo "<h2>🎯 Análise de Padrões Comuns</h2>";
    
    $padroesCampos = [];
    $tiposArquivo = [];
    
    foreach ($relatóriosJSON as $relatorioFile) {
        $relatorio = json_decode(file_get_contents($relatorioFile), true);
        if ($relatorio && isset($relatorio['analise'])) {
            $numeroCampos = $relatorio['analise']['numero_campos'];
            $ehCabecalho = $relatorio['analise']['eh_cabecalho'] ? 'COM cabeçalho' : 'SEM cabeçalho';
            
            $chave = "$numeroCampos campos - $ehCabecalho";
            if (!isset($padroesCampos[$chave])) {
                $padroesCampos[$chave] = 0;
            }
            $padroesCampos[$chave]++;
            
            $extensao = $relatorio['extensao'];
            if (!isset($tiposArquivo[$extensao])) {
                $tiposArquivo[$extensao] = 0;
            }
            $tiposArquivo[$extensao]++;
        }
    }
    
    echo "<h3>📊 Padrões de Estrutura Mais Comuns:</h3>";
    echo "<table>";
    echo "<tr><th>Estrutura</th><th>Quantidade</th><th>Percentual</th></tr>";
    
    arsort($padroesCampos);
    $total = array_sum($padroesCampos);
    
    foreach ($padroesCampos as $padrao => $quantidade) {
        $percentual = round(($quantidade / $total) * 100, 1);
        echo "<tr>";
        echo "<td>$padrao</td>";
        echo "<td>$quantidade</td>";
        echo "<td>{$percentual}%</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>📁 Tipos de Arquivo:</h3>";
    echo "<table>";
    echo "<tr><th>Extensão</th><th>Quantidade</th><th>Percentual</th></tr>";
    
    arsort($tiposArquivo);
    $totalTipos = array_sum($tiposArquivo);
    
    foreach ($tiposArquivo as $tipo => $quantidade) {
        $percentual = round(($quantidade / $totalTipos) * 100, 1);
        echo "<tr>";
        echo "<td>.$tipo</td>";
        echo "<td>$quantidade</td>";
        echo "<td>{$percentual}%</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
}

// Ações em lote
echo "<div class='section'>";
echo "<h2>⚙️ Ações em Lote</h2>";
echo "<p>Gerencie todos os arquivos não reconhecidos de uma vez:</p>";

echo "<a href='limpar_padroes_nao_reconhecidos.php' class='btn btn-danger' onclick='return confirm(\"Tem certeza que deseja limpar todos os arquivos salvos?\")'>🗑️ Limpar Todos</a>";
echo "<a href='exportar_padroes.php' class='btn btn-success'>📤 Exportar Análises</a>";
echo "<a href='gerar_relatorio_padroes.php' class='btn btn-warning'>📊 Gerar Relatório Completo</a>";
echo "</div>";

echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='index.php' class='btn btn-primary'>🏠 Voltar ao Sistema</a>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
