<?php
/**
 * Gerenciador de Arquivos IN-1888
 * 
 * Interface moderna para visualizar, analisar e gerenciar arquivos de saída
 */

define('OUTPUT_DIR', 'output/');

// Função para formatar tamanho de arquivo
function formatFileSize($bytes) {
    if ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

// Função para analisar conteúdo do arquivo
function analyzeFile($filePath) {
    $content = file_get_contents($filePath);
    $lines = explode("\n", $content);
    $totalLines = count($lines);
    
    // Conta tipos de registro
    $registros = ['0110' => 0, '0120' => 0, '0410' => 0, '0510' => 0, 'outros' => 0];
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;
        
        $parts = explode('|', $line);
        if (count($parts) > 0) {
            $tipo = $parts[0];
            if (isset($registros[$tipo])) {
                $registros[$tipo]++;
            } else {
                $registros['outros']++;
            }
        }
    }
    
    return [
        'totalLines' => $totalLines,
        'registros' => $registros,
        'firstLines' => array_slice($lines, 0, 5)
    ];
}

// AJAX para visualização de arquivos
if (isset($_GET['ajax']) && $_GET['ajax'] === 'view' && isset($_GET['file'])) {
    $viewFile = basename($_GET['file']);
    $viewPath = OUTPUT_DIR . $viewFile;
    
    if (file_exists($viewPath) && pathinfo($viewPath, PATHINFO_EXTENSION) === 'txt') {
        $analysis = analyzeFile($viewPath);
        $content = file_get_contents($viewPath);
        $lines = explode("\n", $content);
        $previewLines = array_slice($lines, 0, 20); // Primeiras 20 linhas
        
        $preview = '';
        foreach ($previewLines as $index => $line) {
            $lineNum = $index + 1;
            $preview .= sprintf("%3d: %s\n", $lineNum, $line);
        }
        
        if (count($lines) > 20) {
            $preview .= "\n... (mais " . (count($lines) - 20) . " linhas)";
        }
        
        header('Content-Type: application/json');
        echo json_encode([
            'totalLines' => $analysis['totalLines'],
            'registros' => $analysis['registros'],
            'content' => $preview
        ]);
        exit;
    }
}

// AJAX para limpar todos os arquivos
if (isset($_GET['ajax']) && $_GET['ajax'] === 'clear_all') {
    header('Content-Type: application/json');

    if (!is_dir(OUTPUT_DIR)) {
        echo json_encode([
            'success' => false,
            'message' => 'Diretório de saída não existe!'
        ]);
        exit;
    }

    $files = scandir(OUTPUT_DIR);
    $txtFiles = array_filter($files, function($file) {
        return pathinfo($file, PATHINFO_EXTENSION) === 'txt' && $file !== '.' && $file !== '..';
    });

    $deletedCount = 0;
    $errors = [];

    foreach ($txtFiles as $file) {
        $filePath = OUTPUT_DIR . $file;
        if (file_exists($filePath) && unlink($filePath)) {
            $deletedCount++;
        } else {
            $errors[] = $file;
        }
    }

    if (count($errors) > 0) {
        echo json_encode([
            'success' => false,
            'message' => 'Erro ao excluir alguns arquivos: ' . implode(', ', $errors)
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'message' => "Todos os {$deletedCount} arquivo(s) foram excluídos com sucesso!"
        ]);
    }
    exit;
}

?>
<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciador de Arquivos IN-1888</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/semantic-ui@2.4.2/dist/semantic.min.css">
    <link rel="stylesheet" href="verificar_arquivos_responsive.css">
    <style>
        body {
            background-color: #1a1a1a;
            color: #f5f5f5;
            min-height: 100vh;
        }

        .ui.container {
            background-color: transparent;
        }

        .ui.segment {
            background-color: #2a2a2a !important;
            color: #f5f5f5 !important;
            border: 1px solid #444 !important;
        }

        .ui.message {
            background-color: #2a2a2a !important;
            color: #f5f5f5 !important;
            border: 1px solid #444 !important;
        }

        .ui.header {
            color: #f5f5f5 !important;
        }

        .ui.header .sub.header {
            color: #ccc !important;
        }

        .ui.header i.icon {
            color: #2185d0 !important;
        }

        /* Estatísticas */
        .ui.statistics {
            margin: 2em 0 !important;
            justify-content: space-between !important;
        }

        .ui.statistic {
            background: linear-gradient(135deg, #333 0%, #2a2a2a 100%) !important;
            color: #f5f5f5 !important;
            padding: 2em 1.5em !important;
            border-radius: 12px !important;
            border: 1px solid #444 !important;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4) !important;
            text-align: center !important;
            transition: all 0.3s ease !important;
            flex: 1 !important;
            margin: 0 0.5em !important;
        }

        .ui.statistic:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.5) !important;
            border-color: #2185d0 !important;
        }

        .ui.statistic>.value {
            color: #f5f5f5 !important;
            font-weight: bold !important;
            font-size: 2.5em !important;
            margin-bottom: 0.2em !important;
        }

        .ui.statistic>.value i.icon {
            color: #2185d0 !important;
            margin-right: 0.3em !important;
        }

        .ui.statistic>.label {
            color: #bbb !important;
            font-size: 1.1em !important;
            text-transform: uppercase !important;
            letter-spacing: 1px !important;
            font-weight: 500 !important;
        }

        .ui.three.statistics {
            display: flex !important;
            justify-content: space-between !important;
            align-items: stretch !important;
            margin: 2em 0 !important;
            gap: 1em !important;
        }

        .ui.three.statistics .ui.statistic {
            flex: 1 !important;
            margin: 0 !important;
            min-width: 0 !important;
        }



        /* Inputs e Dropdowns */
        .ui.input input {
            background-color: #333 !important;
            color: #f5f5f5 !important;
            border: 1px solid #555 !important;
        }

        .ui.input input:focus {
            background-color: #3a3a3a !important;
            border-color: #2185d0 !important;
        }

        .ui.dropdown {
            background-color: #333 !important;
            color: #f5f5f5 !important;
            border: 1px solid #555 !important;
        }

        .ui.dropdown:hover {
            background-color: #3a3a3a !important;
        }

        .ui.dropdown .menu {
            background-color: #2a2a2a !important;
            border: 1px solid #555 !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4) !important;
        }

        .ui.dropdown .menu .item {
            color: #f5f5f5 !important;
            border-bottom: 1px solid #444 !important;
        }

        .ui.dropdown .menu .item:hover {
            background-color: #3a3a3a !important;
        }

        .ui.dropdown .text {
            color: #f5f5f5 !important;
        }

        /* Tabela Profissional */
        .ui.table {
            background-color: #2a2a2a !important;
            color: #f5f5f5 !important;
            border: 1px solid #444 !important;
            border-radius: 8px !important;
            overflow: hidden !important;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3) !important;
        }

        .ui.table thead th {
            background: linear-gradient(135deg, #333 0%, #2a2a2a 100%) !important;
            color: #f5f5f5 !important;
            border-bottom: 2px solid #2185d0 !important;
            border-right: 1px solid #444 !important;
            font-weight: bold !important;
            text-transform: uppercase !important;
            letter-spacing: 0.5px !important;
            padding: 1.2em 1em !important;
            font-size: 0.9em !important;
        }

        .ui.table thead th:last-child {
            border-right: none !important;
        }

        .ui.table thead th i.icon {
            color: #2185d0 !important;
            margin-right: 0.5em !important;
        }

        .ui.table tbody tr {
            border-bottom: 1px solid #444 !important;
            transition: all 0.2s ease !important;
        }

        .ui.table tbody tr:hover {
            background-color: #333 !important;
            transform: scale(1.001) !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
        }

        .ui.table tbody tr:nth-child(even) {
            background-color: rgba(255, 255, 255, 0.02) !important;
        }

        .ui.table tbody td {
            border-right: 1px solid #444 !important;
            color: #f5f5f5 !important;
            padding: 1em !important;
            vertical-align: middle !important;
        }

        .ui.table tbody td:last-child {
            border-right: none !important;
        }

        .ui.table .ui.header {
            color: #f5f5f5 !important;
            margin: 0 !important;
        }

        .ui.table .ui.header .content {
            color: #f5f5f5 !important;
        }

        .ui.table .ui.header i.icon {
            color: #2185d0 !important;
        }

        /* Mini statistic dentro da tabela */
        .ui.table .ui.mini.statistic {
            background-color: rgba(255, 255, 255, 0.05) !important;
            padding: 0.5em !important;
            border-radius: 5px !important;
            border: 1px solid #555 !important;
            display: inline-block !important;
        }

        .ui.table .ui.mini.statistic .value {
            color: #2185d0 !important;
            font-size: 1.2em !important;
            font-weight: bold !important;
        }

        .ui.table .ui.mini.statistic .label {
            color: #bbb !important;
            font-size: 0.7em !important;
            text-transform: uppercase !important;
        }

        /* Botões na tabela */
        .ui.table .ui.buttons {
            margin: 0 !important;
        }

        .ui.table .ui.mini.button {
            padding: 0.6em 1em !important;
            font-size: 0.8em !important;
            margin: 0 0.2em !important;
        }

        /* Mini Statistics dentro dos cards */
        .ui.mini.statistics .statistic {
            background-color: rgba(255, 255, 255, 0.05) !important;
            padding: 0.8em !important;
            border-radius: 5px !important;
            margin: 0.2em !important;
            border: 1px solid #555 !important;
        }

        .ui.mini.statistics .statistic>.value {
            color: #f5f5f5 !important;
            font-size: 1.2em !important;
        }

        .ui.mini.statistics .statistic>.label {
            color: #ccc !important;
            font-size: 0.8em !important;
        }

        /* Labels coloridas */
        .ui.label {
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
        }

        .ui.green.label {
            background-color: #21ba45 !important;
            color: white !important;
        }

        .ui.red.label {
            background-color: #db2828 !important;
            color: white !important;
        }

        .ui.blue.label {
            background-color: #2185d0 !important;
            color: white !important;
        }

        .ui.orange.label {
            background-color: #f2711c !important;
            color: white !important;
        }

        /* Botões */
        .ui.button {
            background-color: #555 !important;
            color: #f5f5f5 !important;
            border: 1px solid #666 !important;
        }

        .ui.button:hover {
            background-color: #666 !important;
        }

        .ui.primary.button {
            background-color: #2185d0 !important;
            color: white !important;
        }

        .ui.primary.button:hover {
            background-color: #1678c2 !important;
        }

        .ui.secondary.button {
            background-color: #555 !important;
            color: white !important;
        }

        .ui.secondary.button:hover {
            background-color: #666 !important;
        }

        .ui.red.button {
            background-color: #db2828 !important;
            color: white !important;
        }

        .ui.red.button:hover {
            background-color: #d01919 !important;
        }

        /* Placeholder */
        .ui.placeholder.segment {
            background-color: #2a2a2a !important;
            color: #f5f5f5 !important;
            border: 2px dashed #555 !important;
        }

        .ui.placeholder .header {
            color: #ccc !important;
        }

        /* Divider */
        .ui.divider {
            border-top: 1px solid #444 !important;
            border-bottom: 1px solid #444 !important;
        }

        /* Text */
        .ui.text {
            color: #ccc !important;
        }

        /* Icons */
        i.icon {
            color: #f5f5f5 !important;
        }

        /* Modal */
        .ui.modal {
            background-color: #2a2a2a !important;
            color: #f5f5f5 !important;
            border: 1px solid #444 !important;
        }

        .ui.modal>.header {
            background-color: #333 !important;
            color: #f5f5f5 !important;
            border-bottom: 1px solid #444 !important;
        }

        .ui.modal>.content {
            background-color: #2a2a2a !important;
        }

        .ui.modal>.actions {
            background-color: #333 !important;
            border-top: 1px solid #444 !important;
        }

        /* Vertical Menu */
        .ui.vertical.menu {
            background-color: #333 !important;
            border: 1px solid #444 !important;
        }

        .ui.vertical.menu .item {
            color: #f5f5f5 !important;
            border-bottom: 1px solid #444 !important;
        }

        .ui.vertical.menu .item:hover {
            background-color: #3a3a3a !important;
        }

        .ui.vertical.menu .header.item {
            background-color: #222 !important;
            color: #f5f5f5 !important;
            font-weight: bold !important;
        }

        /* Preview Code */
        .preview-code {
            background-color: #1a1a1a !important;
            color: #f5f5f5 !important;
            font-family: 'Courier New', monospace !important;
            border: 1px solid #444 !important;
            border-radius: 5px !important;
        }

        .preview-code pre {
            color: #f5f5f5 !important;
            background-color: transparent !important;
        }

        /* Negative Message */
        .ui.negative.message {
            background-color: #2c1e1e !important;
            color: #ff6b6b !important;
            border: 1px solid #5a2d2d !important;
        }

        .ui.negative.message .header {
            color: #ff6b6b !important;
        }
    </style>
</head>

<body>
    <div class="ui container" style="padding: 2em 0;">
        <div class="ui segment">
            <div class="ui grid">
                <div class="sixteen wide column">
                    <h1 class="ui huge header">
                        <i class="folder open icon"></i>
                        <div class="content">
                            Gerenciador de Arquivos IN-1888
                            <div class="sub header">Visualize, analise e gerencie seus arquivos de saída</div>
                        </div>
                    </h1>
                </div>
            </div>

            <!-- Estatísticas Gerais -->
            <div class="ui grid">
                <div class="sixteen wide column">
                    <?php
                    $totalFiles = 0;
                    $totalSize = 0;
                    $totalRecords = 0;
                    
                    if (is_dir(OUTPUT_DIR)) {
                        $files = scandir(OUTPUT_DIR);
                        $txtFiles = array_filter($files, function($file) {
                            return pathinfo($file, PATHINFO_EXTENSION) === 'txt' && $file !== '.' && $file !== '..';
                        });
                        
                        $totalFiles = count($txtFiles);
                        
                        foreach ($txtFiles as $file) {
                            $filePath = OUTPUT_DIR . $file;
                            $totalSize += filesize($filePath);
                            $analysis = analyzeFile($filePath);
                            $totalRecords += $analysis['totalLines'];
                        }
                    }
                    ?>

                    <div class="ui three statistics">
                        <div class="ui statistic">
                            <div class="value">
                                <i class="file icon"></i>
                                <?php echo $totalFiles; ?>
                            </div>
                            <div class="label">Arquivos</div>
                        </div>
                        <div class="ui statistic">
                            <div class="value">
                                <i class="database icon"></i>
                                <?php echo number_format($totalRecords); ?>
                            </div>
                            <div class="label">Registros</div>
                        </div>
                        <div class="ui statistic">
                            <div class="value">
                                <i class="hdd icon"></i>
                                <?php echo formatFileSize($totalSize); ?>
                            </div>
                            <div class="label">Tamanho Total</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="ui divider"></div>

            <!-- Filtros e Busca -->
            <div class="ui grid">
                <div class="ten wide column">
                    <div class="ui icon input fluid">
                        <input type="text" id="searchFiles" placeholder="Buscar arquivos...">
                        <i class="search icon"></i>
                    </div>
                </div>
                <div class="six wide column">
                    <div class="ui dropdown selection fluid" id="sortDropdown">
                        <input type="hidden" name="sort">
                        <i class="dropdown icon"></i>
                        <div class="default text">Ordenar por...</div>
                        <div class="menu">
                            <div class="item" data-value="name">Nome</div>
                            <div class="item" data-value="size">Tamanho</div>
                            <div class="item" data-value="date">Data</div>
                            <div class="item" data-value="records">Registros</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="ui divider"></div>

            <!-- Tabela de Arquivos -->
            <div id="filesList">
                <?php
                if (is_dir(OUTPUT_DIR)) {
                    if (count($txtFiles) > 0) {
                        echo '<table class="ui celled striped table" id="filesTable">';
                        echo '<thead>';
                        echo '<tr>';
                        echo '<th><i class="file icon"></i> Arquivo</th>';
                        echo '<th><i class="calendar icon"></i> Período</th>';
                        echo '<th><i class="list icon"></i> Registros</th>';
                        echo '<th><i class="hdd icon"></i> Tamanho</th>';
                        echo '<th><i class="tags icon"></i> Tipos</th>';
                        echo '<th><i class="clock icon"></i> Modificado</th>';
                        echo '<th><i class="setting icon"></i> Ações</th>';
                        echo '</tr>';
                        echo '</thead>';
                        echo '<tbody>';

                        foreach ($txtFiles as $file) {
                            $filePath = OUTPUT_DIR . $file;
                            $fileSize = filesize($filePath);
                            $fileTime = date('d/m/Y H:i:s', filemtime($filePath));
                            $analysis = analyzeFile($filePath);

                            // Determina o período do arquivo
                            $periodo = 'Desconhecido';
                            if (preg_match('/(\w+)(\d{4})/', $file, $matches)) {
                                $meses = [
                                    'JANEIRO' => 'Janeiro', 'FEVEREIRO' => 'Fevereiro', 'MARCO' => 'Março',
                                    'ABRIL' => 'Abril', 'MAIO' => 'Maio', 'JUNHO' => 'Junho',
                                    'JULHO' => 'Julho', 'AGOSTO' => 'Agosto', 'SETEMBRO' => 'Setembro',
                                    'OUTUBRO' => 'Outubro', 'NOVEMBRO' => 'Novembro', 'DEZEMBRO' => 'Dezembro'
                                ];
                                $mesKey = strtoupper($matches[1]);
                                $ano = $matches[2];
                                $periodo = (isset($meses[$mesKey]) ? $meses[$mesKey] : $matches[1]) . ' ' . $ano;
                            }

                            echo '<tr data-filename="' . htmlspecialchars($file) . '" data-size="' . $fileSize . '" data-date="' . filemtime($filePath) . '" data-records="' . $analysis['totalLines'] . '">';

                            // Coluna Arquivo
                            echo '<td>';
                            echo '<div class="ui header">';
                            echo '<i class="file text icon"></i>';
                            echo '<div class="content">';
                            echo htmlspecialchars($file);
                            echo '</div>';
                            echo '</div>';
                            echo '</td>';

                            // Coluna Período
                            echo '<td>' . $periodo . '</td>';

                            // Coluna Registros
                            echo '<td>';
                            echo '<div class="ui statistic mini">';
                            echo '<div class="value">' . number_format($analysis['totalLines']) . '</div>';
                            echo '<div class="label">linhas</div>';
                            echo '</div>';
                            echo '</td>';

                            // Coluna Tamanho
                            echo '<td>' . formatFileSize($fileSize) . '</td>';

                            // Coluna Tipos de Registro
                            echo '<td>';
                            $labels = [
                                '0110' => ['Compras', 'green'],
                                '0120' => ['Vendas', 'red'],
                                '0410' => ['Transferências', 'blue'],
                                '0510' => ['Retiradas', 'orange']
                            ];
                            foreach ($analysis['registros'] as $tipo => $count) {
                                if ($count > 0 && $tipo !== 'outros' && isset($labels[$tipo])) {
                                    echo '<div class="ui mini ' . $labels[$tipo][1] . ' label" style="margin: 1px;">';
                                    echo $labels[$tipo][0] . ': ' . $count;
                                    echo '</div>';
                                }
                            }
                            echo '</td>';

                            // Coluna Modificado
                            echo '<td>' . $fileTime . '</td>';

                            // Coluna Ações
                            echo '<td>';
                            echo '<div class="ui buttons mini">';
                            echo '<a href="download.php?file=' . urlencode($file) . '" class="ui primary button">';
                            echo '<i class="download icon"></i> Download';
                            echo '</a>';
                            echo '<button class="ui secondary button" onclick="viewFile(\'' . htmlspecialchars($file) . '\')">';
                            echo '<i class="eye icon"></i> Visualizar';
                            echo '</button>';
                            echo '</div>';
                            echo '</td>';

                            echo '</tr>';
                        }

                        echo '</tbody>';
                        echo '</table>';
                    } else {
                        echo '<div class="ui placeholder segment">';
                        echo '<div class="ui icon header">';
                        echo '<i class="file outline icon"></i>';
                        echo 'Nenhum arquivo encontrado';
                        echo '</div>';
                        echo '<div class="ui primary button" onclick="window.location.href=\'index.php\'">';
                        echo '<i class="plus icon"></i> Processar Arquivos';
                        echo '</div>';
                        echo '</div>';
                    }
                } else {
                    echo '<div class="ui negative message">';
                    echo '<div class="header">Erro!</div>';
                    echo '<p>Diretório de saída não existe!</p>';
                    echo '</div>';
                }
                ?>
            </div>

            <div class="ui divider"></div>

            <!-- Botões de Ação -->
            <div class="ui grid">
                <div class="eight wide column">
                    <a href="index.php" class="ui large button">
                        <i class="arrow left icon"></i> Voltar ao Sistema
                    </a>
                </div>
                <div class="eight wide column" style="text-align: right;">
                    <button class="ui red button" onclick="confirmClearAll()">
                        <i class="trash icon"></i> Limpar Todos
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Visualização -->
    <div class="ui large modal" id="fileViewModal">
        <div class="header">
            <i class="file text icon"></i>
            <span id="modalFileName">Visualizar Arquivo</span>
        </div>
        <div class="content">
            <div class="ui grid">
                <div class="four wide column">
                    <div class="ui statistics">
                        <div class="statistic">
                            <div class="value" id="modalFileLines">0</div>
                            <div class="label">Linhas</div>
                        </div>
                    </div>
                    <div class="ui vertical menu fluid" style="margin-top: 20px;">
                        <div class="header item">Tipos de Registro</div>
                        <div class="item" id="modalRegistros">
                            <!-- Será preenchido via JavaScript -->
                        </div>
                    </div>
                </div>
                <div class="twelve wide column">
                    <div class="ui segment preview-code" style="max-height: 500px; overflow-y: auto;">
                        <pre id="modalFileContent" style="margin: 0; white-space: pre-wrap;"></pre>
                    </div>
                </div>
            </div>
        </div>
        <div class="actions">
            <div class="ui buttons">
                <button class="ui button" onclick="$('#fileViewModal').modal('hide')">Fechar</button>
                <div class="or" data-text="ou"></div>
                <button class="ui primary button" id="modalDownloadBtn">
                    <i class="download icon"></i> Download
                </button>
            </div>
        </div>
    </div>

    <!-- Modal de Confirmação -->
    <div class="ui small modal" id="confirmModal">
        <div class="header">
            <i class="warning sign icon"></i>
            Confirmar Ação
        </div>
        <div class="content">
            <p>Tem certeza que deseja excluir todos os arquivos? Esta ação não pode ser desfeita.</p>
        </div>
        <div class="actions">
            <div class="ui buttons">
                <button class="ui button" onclick="$('#confirmModal').modal('hide')">Cancelar</button>
                <div class="or" data-text="ou"></div>
                <button class="ui red button" onclick="clearAllFiles()">
                    <i class="trash icon"></i> Excluir Todos
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/semantic-ui@2.4.2/dist/semantic.min.js"></script>
    <script>
        $(document).ready(function () {
            // Inicializa dropdowns
            $('.ui.dropdown').dropdown();

            // Busca em tempo real
            $('#searchFiles').on('input', function () {
                const searchTerm = $(this).val().toLowerCase();
                $('#filesTable tbody tr').each(function () {
                    const fileName = $(this).data('filename').toLowerCase();
                    if (fileName.includes(searchTerm)) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            });

            // Ordenação
            $('#sortDropdown').dropdown({
                onChange: function (value) {
                    sortFiles(value);
                }
            });
        });

        function sortFiles(criteria) {
            const tbody = $('#filesTable tbody');
            const rows = tbody.find('tr').get();

            rows.sort(function (a, b) {
                let aVal, bVal;

                switch (criteria) {
                    case 'name':
                        aVal = $(a).data('filename').toLowerCase();
                        bVal = $(b).data('filename').toLowerCase();
                        return aVal.localeCompare(bVal);
                    case 'size':
                        aVal = parseInt($(a).data('size'));
                        bVal = parseInt($(b).data('size'));
                        return bVal - aVal; // Maior primeiro
                    case 'date':
                        aVal = parseInt($(a).data('date'));
                        bVal = parseInt($(b).data('date'));
                        return bVal - aVal; // Mais recente primeiro
                    case 'records':
                        aVal = parseInt($(a).data('records'));
                        bVal = parseInt($(b).data('records'));
                        return bVal - aVal; // Mais registros primeiro
                    default:
                        return 0;
                }
            });

            $.each(rows, function (index, row) {
                tbody.append(row);
            });
        }

        function viewFile(fileName) {
            $('#modalFileName').text(fileName);
            $('#modalDownloadBtn').attr('onclick',
                `window.open('download.php?file=${encodeURIComponent(fileName)}', '_blank')`);

            // Carrega conteúdo via AJAX
            $.get('?ajax=view&file=' + encodeURIComponent(fileName))
                .done(function (data) {
                    const response = JSON.parse(data);
                    $('#modalFileLines').text(response.totalLines.toLocaleString());
                    $('#modalFileContent').text(response.content);

                    // Atualiza registros
                    let registrosHtml = '';
                    const labels = {
                        '0110': ['Compras', 'green'],
                        '0120': ['Vendas', 'red'],
                        '0410': ['Transferências', 'blue'],
                        '0510': ['Retiradas', 'orange']
                    };

                    for (const [tipo, count] of Object.entries(response.registros)) {
                        if (count > 0 && labels[tipo]) {
                            registrosHtml +=
                                `<div class="ui mini ${labels[tipo][1]} label" style="margin: 2px;">${labels[tipo][0]}: ${count}</div>`;
                        }
                    }
                    $('#modalRegistros').html(registrosHtml);
                })
                .fail(function () {
                    $('#modalFileContent').text('Erro ao carregar arquivo.');
                });

            $('#fileViewModal').modal('show');
        }

        function confirmClearAll() {
            $('#confirmModal').modal('show');
        }

        function clearAllFiles() {
            // Mostra loading
            $('#confirmModal .ui.red.button').addClass('loading');

            // Implementa limpeza via AJAX
            $.get('?ajax=clear_all')
                .done(function (response) {
                    try {
                        const result = JSON.parse(response);
                        if (result.success) {
                            // Remove todas as linhas da tabela
                            $('#filesTable tbody').empty();

                            // Atualiza estatísticas para 0
                            $('#filesList .ui.statistic .value').each(function () {
                                const iconClass = $(this).find('i').attr('class');
                                $(this).html('<i class="' + iconClass + '"></i> 0');
                            });

                            // Mostra placeholder se não há arquivos
                            $('#filesList').html(`
                                <div class="ui placeholder segment">
                                    <div class="ui icon header">
                                        <i class="file outline icon"></i>
                                        Nenhum arquivo encontrado
                                    </div>
                                    <div class="ui primary button" onclick="window.location.href='index.php'">
                                        <i class="plus icon"></i> Processar Arquivos
                                    </div>
                                </div>
                            `);

                            // Mostra mensagem de sucesso
                            showMessage('success', 'Sucesso!', result.message);
                        } else {
                            showMessage('error', 'Erro!', result.message);
                        }
                    } catch (e) {
                        showMessage('error', 'Erro!', 'Resposta inválida do servidor.');
                    }
                })
                .fail(function () {
                    showMessage('error', 'Erro!', 'Erro ao comunicar com o servidor.');
                })
                .always(function () {
                    $('#confirmModal .ui.red.button').removeClass('loading');
                    $('#confirmModal').modal('hide');
                });
        }

        function showMessage(type, title, message) {
            const messageClass = type === 'success' ? 'positive' : 'negative';
            const icon = type === 'success' ? 'check circle' : 'exclamation triangle';

            const messageHtml = `
                <div class="ui ${messageClass} message" style="margin-top: 1em;">
                    <i class="${icon} icon"></i>
                    <div class="content">
                        <div class="header">${title}</div>
                        <p>${message}</p>
                    </div>
                </div>
            `;

            // Remove mensagens anteriores
            $('.ui.message').remove();

            // Adiciona nova mensagem
            $('.ui.segment').prepend(messageHtml);

            // Remove mensagem após 5 segundos
            setTimeout(function () {
                $('.ui.message').fadeOut(500, function () {
                    $(this).remove();
                });
            }, 5000);
        }
    </script>
</body>

</html>