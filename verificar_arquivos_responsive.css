/* Responsividade para Cards de Estatísticas */

/* Desktop - 3 cards em linha */
.ui.three.statistics {
    display: flex !important;
    justify-content: space-between !important;
    align-items: stretch !important;
    margin: 2em 0 !important;
    gap: 1em !important;
}

.ui.three.statistics .ui.statistic {
    flex: 1 !important;
    margin: 0 !important;
    min-width: 0 !important;
}

/* Mobile - Cards em coluna única */
@media (max-width: 768px) {
    .ui.three.statistics {
        flex-direction: column !important;
        gap: 1em !important;
    }
    
    .ui.three.statistics .ui.statistic {
        flex: none !important;
        width: 100% !important;
        margin: 0 !important;
    }
    
    .ui.statistic {
        padding: 1.5em 1em !important;
    }
    
    .ui.statistic > .value {
        font-size: 2em !important;
    }
    
    .ui.table {
        font-size: 0.9em !important;
    }
    
    .ui.table .ui.mini.button {
        padding: 0.5em 0.8em !important;
        font-size: 0.7em !important;
    }
}

/* Tablet - Ajustes intermediários */
@media (min-width: 769px) and (max-width: 992px) {
    .ui.statistic {
        padding: 1.8em 1.2em !important;
    }
    
    .ui.statistic > .value {
        font-size: 2.2em !important;
    }
    
    .ui.statistic > .label {
        font-size: 1em !important;
    }
}
