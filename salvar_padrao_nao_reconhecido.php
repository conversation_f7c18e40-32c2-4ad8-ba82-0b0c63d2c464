<?php
/**
 * Sistema para Salvar Padrões Não Reconhecidos
 * 
 * Registra arquivos que não foram reconhecidos para análise posterior
 */

if (!isset($_GET['arquivo']) || empty($_GET['arquivo'])) {
    die("Arquivo não especificado");
}

$arquivo = $_GET['arquivo'];
if (!file_exists($arquivo)) {
    die("Arquivo não encontrado: $arquivo");
}

// Cria diretório para padrões não reconhecidos
$diretorioAnalise = 'padroes_nao_reconhecidos';
if (!is_dir($diretorioAnalise)) {
    mkdir($diretorioAnalise, 0755, true);
}

$nomeArquivo = basename($arquivo);
$timestamp = date('Y-m-d_H-i-s');
$arquivoDestino = $diretorioAnalise . '/' . $timestamp . '_' . $nomeArquivo;

// Copia o arquivo para análise
copy($arquivo, $arquivoDestino);

// Lê as primeiras linhas para análise
$linhas = [];
$handle = fopen($arquivo, 'r');
if ($handle) {
    for ($i = 0; $i < 20 && ($linha = fgets($handle)) !== false; $i++) {
        $linhas[] = trim($linha);
    }
    fclose($handle);
}

// Cria relatório de análise
$relatorio = [
    'timestamp' => date('Y-m-d H:i:s'),
    'arquivo_original' => $arquivo,
    'arquivo_copia' => $arquivoDestino,
    'tamanho' => filesize($arquivo),
    'extensao' => pathinfo($arquivo, PATHINFO_EXTENSION),
    'primeiras_linhas' => $linhas,
    'analise' => [
        'primeira_linha' => $linhas[0] ?? '',
        'campos_primeira_linha' => str_getcsv($linhas[0] ?? ''),
        'numero_campos' => count(str_getcsv($linhas[0] ?? '')),
        'eh_cabecalho' => detectarSeCabecalho(str_getcsv($linhas[0] ?? '')),
        'padroes_similares' => calcularSimilaridadeComPadroes(str_getcsv($linhas[0] ?? ''))
    ]
];

// Salva relatório JSON
$arquivoRelatorio = $diretorioAnalise . '/' . $timestamp . '_relatorio.json';
file_put_contents($arquivoRelatorio, json_encode($relatorio, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

// Atualiza log geral
$logGeral = $diretorioAnalise . '/log_padroes_nao_reconhecidos.txt';
$entradaLog = "[{$timestamp}] {$nomeArquivo} - " . count($relatorio['analise']['campos_primeira_linha']) . " campos - " . 
              ($relatorio['analise']['eh_cabecalho'] ? 'COM cabeçalho' : 'SEM cabeçalho') . "\n";
file_put_contents($logGeral, $entradaLog, FILE_APPEND | LOCK_EX);

echo "<!DOCTYPE html>";
echo "<html><head><title>Padrão Salvo para Análise - IN-1888</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
.container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
.ok { color: green; font-weight: bold; }
.info { color: blue; font-weight: bold; }
.section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
.preview { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px; }
.btn { display: inline-block; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; color: white; font-weight: bold; }
.btn-primary { background: #2196f3; }
.btn-success { background: #4caf50; }
</style></head><body>";

echo "<div class='container'>";
echo "<h1>💾 Padrão Salvo para Análise</h1>";

echo "<div class='section'>";
echo "<span class='ok'>✅ Arquivo salvo com sucesso para análise posterior!</span><br><br>";

echo "<p><strong>Arquivo original:</strong> $nomeArquivo</p>";
echo "<p><strong>Salvo em:</strong> $arquivoDestino</p>";
echo "<p><strong>Relatório:</strong> $arquivoRelatorio</p>";
echo "<p><strong>Timestamp:</strong> {$relatorio['timestamp']}</p>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>📊 Resumo da Análise</h2>";
echo "<p><strong>Número de campos:</strong> {$relatorio['analise']['numero_campos']}</p>";
echo "<p><strong>Tipo de primeira linha:</strong> " . ($relatorio['analise']['eh_cabecalho'] ? 'Cabeçalho' : 'Dados') . "</p>";
echo "<p><strong>Tamanho do arquivo:</strong> " . number_format($relatorio['tamanho']) . " bytes</p>";

echo "<h3>🔍 Primeira Linha Analisada:</h3>";
echo "<div class='preview'>";
echo htmlspecialchars($relatorio['analise']['primeira_linha']);
echo "</div>";

echo "<h3>📋 Campos Detectados:</h3>";
echo "<div class='preview'>";
foreach ($relatorio['analise']['campos_primeira_linha'] as $index => $campo) {
    echo ($index + 1) . ". " . htmlspecialchars($campo) . "\n";
}
echo "</div>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>🎯 Próximos Passos</h2>";
echo "<ol>";
echo "<li><strong>Análise Manual:</strong> Revisar o arquivo salvo para entender a estrutura</li>";
echo "<li><strong>Implementação:</strong> Criar novo padrão baseado na análise</li>";
echo "<li><strong>Teste:</strong> Validar o novo padrão com o arquivo original</li>";
echo "<li><strong>Documentação:</strong> Atualizar documentação com o novo padrão</li>";
echo "</ol>";

echo "<p><span class='info'>💡 Dica:</span> Todos os arquivos não reconhecidos são salvos automaticamente para facilitar a implementação de novos padrões.</p>";
echo "</div>";

echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='diagnostico_arquivos.php?arquivo=" . urlencode($arquivo) . "' class='btn btn-primary'>🔍 Ver Diagnóstico Completo</a>";
echo "<a href='listar_padroes_nao_reconhecidos.php' class='btn btn-success'>📋 Ver Todos os Padrões Salvos</a>";
echo "<a href='index.php' class='btn btn-primary'>🏠 Voltar ao Sistema</a>";
echo "</div>";

echo "</div>";
echo "</body></html>";

// Funções auxiliares (reutilizadas do diagnóstico)
function detectarSeCabecalho($campos) {
    $indicadoresCabecalho = ['data', 'date', 'time', 'valor', 'value', 'amount', 'quantity', 'symbol', 'pair', 'type', 'side'];
    $indicadoresDados = ['/^\d{4}-\d{2}-\d{2}/', '/^\d{2}\/\d{2}\/\d{4}/', '/^[+-]?\d+\.?\d*$/', '/^[A-Z]{3,4}$/'];
    
    $scoreCabecalho = 0;
    $scoreDados = 0;
    
    foreach ($campos as $campo) {
        $campoLower = strtolower(trim($campo));
        
        foreach ($indicadoresCabecalho as $indicador) {
            if (strpos($campoLower, $indicador) !== false) {
                $scoreCabecalho++;
                break;
            }
        }
        
        foreach ($indicadoresDados as $pattern) {
            if (preg_match($pattern, trim($campo))) {
                $scoreDados++;
                break;
            }
        }
    }
    
    return $scoreCabecalho > $scoreDados;
}

function calcularSimilaridadeComPadroes($campos) {
    $padroes = [
        'binance_geral' => ['Date(UTC)', 'Pair', 'Side', 'Order Amount', 'Order Price', 'Fee', 'Realized PNL'],
        'binance_p2p' => ['Order Number', 'Order Type', 'Asset', 'Fiat', 'Total Price', 'Price', 'Quantity', 'Counterparty', 'Status', 'Created Time'],
        'gate_deposits' => ['Time', 'Currency', 'Amount', 'Address', 'TXID', 'Status']
    ];
    
    $similaridades = [];
    
    foreach ($padroes as $nome => $padraocampos) {
        $intersection = array_intersect(array_map('strtolower', $campos), array_map('strtolower', $padraocampos));
        $union = array_unique(array_merge(array_map('strtolower', $campos), array_map('strtolower', $padraocampos)));
        
        $similaridade = count($union) > 0 ? round((count($intersection) / count($union)) * 100) : 0;
        $similaridades[$nome] = $similaridade;
    }
    
    return $similaridades;
}
?>
