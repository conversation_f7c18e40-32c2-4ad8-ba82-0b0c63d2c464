<?php
/**
 * Script para Resolver Conflito CCXT/React Promise
 * 
 * Resolve o erro: "Class ccxt\pro\Future cannot extend final class React\Promise\Deferred"
 */

echo "<!DOCTYPE html>";
echo "<html><head><title>Resolver Conflito CCXT - IN-1888</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
.container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
.ok { color: green; font-weight: bold; }
.error { color: red; font-weight: bold; }
.warning { color: orange; font-weight: bold; }
.info { color: blue; font-weight: bold; }
.section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
.command { background: #2d3748; color: #e2e8f0; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; }
pre { background: #f5f5f5; padding: 10px; overflow-x: auto; border-radius: 3px; }
.step { background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #4CAF50; }
</style></head><body>";

echo "<div class='container'>";
echo "<h1>🔧 Resolver Conflito CCXT/React Promise</h1>";
echo "<p>Este script resolve o erro de compatibilidade entre CCXT e React/Promise.</p>";

// Diagnóstico do problema
echo "<div class='section'>";
echo "<h2>🔍 Diagnóstico do Problema</h2>";

echo "<div class='step'>";
echo "<h3>❌ Erro Identificado:</h3>";
echo "<code>Fatal error: Class ccxt\\pro\\Future cannot extend final class React\\Promise\\Deferred</code>";
echo "<p><strong>Causa:</strong> Conflito de versões entre CCXT e React/Promise. A versão mais recente do React/Promise tornou a classe Deferred final.</p>";
echo "</div>";

// Verifica versões atuais
echo "<h3>📦 Verificação de Dependências:</h3>";

$composerLock = 'composer.lock';
$vendorDir = 'vendor/';

if (file_exists($composerLock)) {
    $lockData = json_decode(file_get_contents($composerLock), true);
    
    echo "<table border='1' style='width:100%; border-collapse: collapse;'>";
    echo "<tr><th>Pacote</th><th>Versão Atual</th><th>Status</th></tr>";
    
    $packages = $lockData['packages'] ?? [];
    $ccxtVersion = null;
    $reactPromiseVersion = null;
    
    foreach ($packages as $package) {
        if ($package['name'] === 'ccxt/ccxt') {
            $ccxtVersion = $package['version'];
            echo "<tr><td>ccxt/ccxt</td><td>$ccxtVersion</td><td><span class='info'>Instalado</span></td></tr>";
        }
        if ($package['name'] === 'react/promise') {
            $reactPromiseVersion = $package['version'];
            echo "<tr><td>react/promise</td><td>$reactPromiseVersion</td><td><span class='info'>Instalado</span></td></tr>";
        }
    }
    
    echo "</table>";
    
    // Análise de compatibilidade
    if ($ccxtVersion && $reactPromiseVersion) {
        echo "<h3>⚖️ Análise de Compatibilidade:</h3>";
        
        if (version_compare($reactPromiseVersion, '3.0.0', '>=')) {
            echo "<span class='error'>❌ React/Promise v3+ incompatível com CCXT atual</span><br>";
            echo "<p>React/Promise v3+ tornou a classe Deferred final, causando o erro.</p>";
        } else {
            echo "<span class='ok'>✅ Versões compatíveis</span><br>";
        }
    }
    
} else {
    echo "<span class='warning'>⚠️ composer.lock não encontrado</span><br>";
}

echo "</div>";

// Soluções disponíveis
echo "<div class='section'>";
echo "<h2>🛠️ Soluções Disponíveis</h2>";

echo "<div class='step'>";
echo "<h3>✅ Solução 1: Atualizar para CCXT v4+ (Recomendado)</h3>";
echo "<p>CCXT v4+ resolve o conflito com React/Promise v3+</p>";
echo "<div class='command'>composer require ccxt/ccxt:^4.0 react/promise:^2.10</div>";
echo "<div class='command'>composer update</div>";
echo "</div>";

echo "<div class='step'>";
echo "<h3>🔄 Solução 2: Forçar Versão Compatível do React/Promise</h3>";
echo "<p>Mantém CCXT atual mas força versão compatível do React/Promise</p>";
echo "<div class='command'>composer require react/promise:^2.10</div>";
echo "<div class='command'>composer update</div>";
echo "</div>";

echo "<div class='step'>";
echo "<h3>🗑️ Solução 3: Remover e Reinstalar (Limpeza Completa)</h3>";
echo "<p>Remove todas as dependências e reinstala com versões compatíveis</p>";
echo "<div class='command'>rm -rf vendor/ composer.lock</div>";
echo "<div class='command'>composer install</div>";
echo "</div>";

echo "</div>";

// Implementação automática
echo "<div class='section'>";
echo "<h2>🚀 Implementação Automática</h2>";

if (isset($_GET['action'])) {
    $action = $_GET['action'];
    
    switch ($action) {
        case 'update_ccxt':
            echo "<h3>🔄 Atualizando CCXT para v4+...</h3>";
            
            // Executa comando via shell
            $output = [];
            $returnCode = 0;
            
            // Comando para atualizar
            exec('composer require ccxt/ccxt:^4.0 react/promise:^2.10 2>&1', $output, $returnCode);
            
            if ($returnCode === 0) {
                echo "<span class='ok'>✅ CCXT atualizado com sucesso!</span><br>";
                echo "<pre>" . implode("\n", $output) . "</pre>";
                
                // Executa update
                exec('composer update 2>&1', $output2, $returnCode2);
                if ($returnCode2 === 0) {
                    echo "<span class='ok'>✅ Dependências atualizadas!</span><br>";
                } else {
                    echo "<span class='error'>❌ Erro no update</span><br>";
                    echo "<pre>" . implode("\n", $output2) . "</pre>";
                }
            } else {
                echo "<span class='error'>❌ Erro na atualização</span><br>";
                echo "<pre>" . implode("\n", $output) . "</pre>";
            }
            break;
            
        case 'force_compatible':
            echo "<h3>🔧 Forçando versão compatível...</h3>";
            
            $output = [];
            exec('composer require react/promise:^2.10 2>&1', $output, $returnCode);
            
            if ($returnCode === 0) {
                echo "<span class='ok'>✅ React/Promise fixado na v2.10</span><br>";
                echo "<pre>" . implode("\n", $output) . "</pre>";
            } else {
                echo "<span class='error'>❌ Erro ao fixar versão</span><br>";
                echo "<pre>" . implode("\n", $output) . "</pre>";
            }
            break;
            
        case 'clean_install':
            echo "<h3>🗑️ Limpeza completa e reinstalação...</h3>";
            
            // Remove vendor e composer.lock
            if (is_dir('vendor')) {
                echo "<span class='info'>🗑️ Removendo pasta vendor...</span><br>";
                // Função recursiva para remover diretório
                function removeDirectory($dir) {
                    if (is_dir($dir)) {
                        $objects = scandir($dir);
                        foreach ($objects as $object) {
                            if ($object != "." && $object != "..") {
                                if (is_dir($dir . "/" . $object)) {
                                    removeDirectory($dir . "/" . $object);
                                } else {
                                    unlink($dir . "/" . $object);
                                }
                            }
                        }
                        rmdir($dir);
                    }
                }
                removeDirectory('vendor');
            }
            
            if (file_exists('composer.lock')) {
                unlink('composer.lock');
                echo "<span class='info'>🗑️ composer.lock removido</span><br>";
            }
            
            // Reinstala
            $output = [];
            exec('composer install 2>&1', $output, $returnCode);
            
            if ($returnCode === 0) {
                echo "<span class='ok'>✅ Reinstalação concluída!</span><br>";
                echo "<pre>" . implode("\n", $output) . "</pre>";
            } else {
                echo "<span class='error'>❌ Erro na reinstalação</span><br>";
                echo "<pre>" . implode("\n", $output) . "</pre>";
            }
            break;
    }
    
    echo "<br><a href='resolver_conflito_ccxt.php' style='background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔄 Verificar Novamente</a>";
    
} else {
    echo "<p>Escolha uma das soluções abaixo:</p>";
    
    echo "<div style='text-align: center;'>";
    echo "<a href='?action=update_ccxt' style='background: #4CAF50; color: white; padding: 15px 25px; text-decoration: none; border-radius: 5px; margin: 10px; display: inline-block;'>🚀 Solução 1: Atualizar CCXT</a><br>";
    echo "<a href='?action=force_compatible' style='background: #FF9800; color: white; padding: 15px 25px; text-decoration: none; border-radius: 5px; margin: 10px; display: inline-block;'>🔧 Solução 2: Forçar Compatibilidade</a><br>";
    echo "<a href='?action=clean_install' style='background: #f44336; color: white; padding: 15px 25px; text-decoration: none; border-radius: 5px; margin: 10px; display: inline-block;'>🗑️ Solução 3: Limpeza Completa</a>";
    echo "</div>";
}

echo "</div>";

// Teste pós-resolução
echo "<div class='section'>";
echo "<h2>🧪 Teste de Funcionamento</h2>";

if (file_exists('vendor/autoload.php')) {
    echo "<span class='ok'>✅ Autoload disponível</span><br>";
    
    try {
        require_once 'vendor/autoload.php';
        
        // Testa se CCXT carrega sem erro
        if (class_exists('\ccxt\Exchange')) {
            echo "<span class='ok'>✅ CCXT carregado com sucesso</span><br>";
            
            // Testa criação de exchange
            try {
                $exchange = new \ccxt\binance(['sandbox' => true]);
                echo "<span class='ok'>✅ Exchange Binance criada sem erros</span><br>";
            } catch (Exception $e) {
                echo "<span class='warning'>⚠️ Erro ao criar exchange: " . $e->getMessage() . "</span><br>";
            }
            
        } else {
            echo "<span class='error'>❌ CCXT não encontrado</span><br>";
        }
        
        // Testa PhpSpreadsheet
        if (class_exists('\PhpOffice\PhpSpreadsheet\Spreadsheet')) {
            echo "<span class='ok'>✅ PhpSpreadsheet funcionando</span><br>";
        } else {
            echo "<span class='warning'>⚠️ PhpSpreadsheet não encontrado</span><br>";
        }
        
    } catch (Exception $e) {
        echo "<span class='error'>❌ Erro ao carregar dependências: " . $e->getMessage() . "</span><br>";
    }
    
} else {
    echo "<span class='error'>❌ Vendor não encontrado - execute uma das soluções acima</span><br>";
}

echo "</div>";

// Instruções finais
echo "<div class='section'>";
echo "<h2>📋 Próximos Passos</h2>";

echo "<ol>";
echo "<li><strong>Após resolver o conflito:</strong> Teste o sistema principal</li>";
echo "<li><strong>Verifique:</strong> Se o upload de arquivos Excel funciona</li>";
echo "<li><strong>Teste:</strong> Processamento de extratos P2P</li>";
echo "<li><strong>Monitore:</strong> Logs de erro para outros problemas</li>";
echo "</ol>";

echo "<div style='text-align: center; margin-top: 20px;'>";
echo "<a href='index.php' style='background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>← Sistema Principal</a>";
echo "<a href='instalar_dependencias.php' style='background: #2196F3; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔧 Verificar Dependências</a>";
echo "</div>";

echo "</div>";

echo "</div>";
echo "</body></html>";
?>
