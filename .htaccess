# Sistema de Processamento de Extratos IN-1888
# Configurações do Apache para funcionamento adequado do sistema

# Habilita o mod_rewrite
RewriteEngine On

# Define o index.php como página padrão
DirectoryIndex index.php

# Configurações de upload de arquivos
# Permite arquivos de até 50MB
php_value upload_max_filesize 50M
php_value post_max_size 50M
php_value max_execution_time 300
php_value max_input_time 300
php_value memory_limit 256M

# Configurações de segurança
# Protege arquivos de configuração
<Files ".htaccess">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.ini">
    Order Allow,Deny
    Deny from all
</Files>

# Protege diretórios sensíveis
<IfModule mod_rewrite.c>
    # Bloqueia acesso direto a arquivos PHP em uploads (exceto downloads)
    RewriteCond %{REQUEST_URI} ^/uploads/.*\.php$ [NC]
    RewriteRule ^(.*)$ - [F,L]
    
    # Permite apenas arquivos CSV, Excel e TXT no diretório uploads
    RewriteCond %{REQUEST_URI} ^/uploads/.*$ [NC]
    RewriteCond %{REQUEST_URI} !.*\.(csv|txt|xlsx|xls)$ [NC]
    RewriteCond %{REQUEST_METHOD} GET [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# Configurações de MIME types para arquivos suportados
<IfModule mod_mime.c>
    AddType text/csv .csv
    AddType text/plain .txt
    AddType application/vnd.openxmlformats-officedocument.spreadsheetml.sheet .xlsx
    AddType application/vnd.ms-excel .xls
</IfModule>

# Configurações de cache para arquivos estáticos
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>

# Compressão GZIP para melhor performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Headers de segurança
<IfModule mod_headers.c>
    # Previne ataques XSS
    Header always set X-XSS-Protection "1; mode=block"
    
    # Previne MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"
    
    # Força HTTPS (descomente se usar SSL)
    # Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    
    # Controla referrer policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Remove informações do servidor
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# Configurações específicas para downloads
<Directory "output">
    # Permite acesso aos arquivos de saída
    Options -Indexes
    AllowOverride None
    Order Allow,Deny
    Allow from all
    
    # Headers para download forçado de arquivos .txt
    <FilesMatch "\.(txt)$">
        Header set Content-Disposition "attachment"
        Header set Content-Type "application/octet-stream"
    </FilesMatch>
</Directory>

# Permite acesso ao script de download
<Files "download.php">
    Order Allow,Deny
    Allow from all
</Files>

# Permite acesso ao script de diagnóstico
<Files "diagnostico.php">
    Order Allow,Deny
    Allow from all
</Files>

# Configurações para o diretório uploads
<Directory "uploads">
    # Desabilita execução de scripts
    Options -ExecCGI -Indexes
    AllowOverride None
    
    # Permite apenas arquivos CSV, Excel e TXT
    <FilesMatch "\.(?!csv$|txt$|xlsx$|xls$)[^.]+$">
        Order Allow,Deny
        Deny from all
    </FilesMatch>
</Directory>

# Páginas de erro personalizadas (opcional)
ErrorDocument 403 /index.php
ErrorDocument 404 /index.php
ErrorDocument 500 /index.php

# Configurações adicionais de PHP
php_flag display_errors On
php_flag log_errors On
php_value error_log "error_log"

# Define timezone padrão
php_value date.timezone "America/Sao_Paulo"

# Configurações de sessão mais seguras
php_value session.cookie_httponly 1
php_value session.use_strict_mode 1
php_value session.cookie_samesite "Strict" 