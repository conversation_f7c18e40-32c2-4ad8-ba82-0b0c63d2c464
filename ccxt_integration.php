<?php
/**
 * Integração CCXT para Cotações de Criptomoedas
 * Substitui as APIs diretas por uma biblioteca robusta e confiável
 */

// Instalar CCXT: composer require ccxt/ccxt
// ou baixar diretamente: https://github.com/ccxt/ccxt/releases

require_once 'vendor/autoload.php'; // Se instalado via Composer
// ou: include 'ccxt.php'; // Se baixado diretamente

use \ccxt;

/**
 * Classe para gerenciar cotações via CCXT
 */
class CCXTCotacaoManager {
    
    private $exchanges = [];
    private $cacheFile = 'cache_cotacoes_ccxt.json';
    private $cacheValidity = 24 * 3600; // 24 horas
    
    public function __construct() {
        $this->initializeExchanges();
    }
    
    /**
     * Inicializa as exchanges suportadas
     */
    private function initializeExchanges() {
        try {
            // Binance - dados públicos (não precisa de API key)
            $this->exchanges['binance'] = new \ccxt\binance([
                'timeout' => 10000,
                'enableRateLimit' => true,
                'sandbox' => false, // true para testing
            ]);
            
            // Gate.io - dados públicos
            $this->exchanges['gate'] = new \ccxt\gateio([
                'timeout' => 10000,
                'enableRateLimit' => true,
                'sandbox' => false,
            ]);
            
            // Adicione outras exchanges conforme necessário
            $this->exchanges['kraken'] = new \ccxt\kraken([
                'timeout' => 10000,
                'enableRateLimit' => true,
            ]);
            
        } catch (Exception $e) {
            error_log("Erro ao inicializar exchanges CCXT: " . $e->getMessage());
        }
    }
    
    /**
     * Busca cotação usando CCXT com fallback entre exchanges
     */
    public function buscarCotacao($moeda) {
        $moeda = strtoupper($moeda);
        
        // 1. Verifica cache primeiro
        $cotacaoCache = $this->buscarDoCache($moeda);
        if ($cotacaoCache !== false) {
            return $cotacaoCache;
        }
        
        // 2. Lista de pares para tentar
        $pares = [
            $moeda . '/USDT',
            $moeda . '/USD', 
            $moeda . '/BTC',
            $moeda . '/ETH'
        ];
        
        // 3. Tenta em cada exchange na ordem de prioridade
        $exchanges = ['binance', 'gate', 'kraken'];
        
        foreach ($exchanges as $exchangeName) {
            foreach ($pares as $par) {
                try {
                    $exchange = $this->exchanges[$exchangeName];
                    
                    // Carrega mercados se necessário
                    if (empty($exchange->markets)) {
                        $exchange->load_markets();
                    }
                    
                    // Verifica se o par existe nesta exchange
                    if (!isset($exchange->markets[$par])) {
                        continue;
                    }
                    
                    // Busca o ticker (preço atual)
                    $ticker = $exchange->fetch_ticker($par);
                    
                    if ($ticker && $ticker['last'] > 0) {
                        $preco = $ticker['last'];
                        
                        // Converte para USD se necessário
                        if (strpos($par, '/USDT') !== false) {
                            $precoUSD = $preco; // USDT é aprox. 1:1 com USD
                        } elseif (strpos($par, '/USD') !== false) {
                            $precoUSD = $preco;
                        } elseif (strpos($par, '/BTC') !== false) {
                            // Converter BTC para USD
                            $btcPrice = $this->getBTCPrice($exchangeName);
                            $precoUSD = $preco * $btcPrice;
                        } elseif (strpos($par, '/ETH') !== false) {
                            // Converter ETH para USD
                            $ethPrice = $this->getETHPrice($exchangeName);
                            $precoUSD = $preco * $ethPrice;
                        } else {
                            $precoUSD = $preco;
                        }
                        
                        // Salva no cache
                        $this->salvarNoCache($moeda, $precoUSD, $exchangeName, $par);
                        
                        return $precoUSD;
                    }
                    
                } catch (Exception $e) {
                    // Log do erro mas continua tentando outras exchanges
                    error_log("Erro CCXT {$exchangeName} {$par}: " . $e->getMessage());
                    continue;
                }
            }
        }
        
        // Se chegou até aqui, não encontrou em nenhuma exchange
        return false;
    }
    
    /**
     * Busca preço do BTC para conversões
     */
    private function getBTCPrice($exchangeName) {
        try {
            $exchange = $this->exchanges[$exchangeName];
            $ticker = $exchange->fetch_ticker('BTC/USDT');
            return $ticker['last'];
        } catch (Exception $e) {
            return 50000; // Fallback aproximado
        }
    }
    
    /**
     * Busca preço do ETH para conversões
     */
    private function getETHPrice($exchangeName) {
        try {
            $exchange = $this->exchanges[$exchangeName];
            $ticker = $exchange->fetch_ticker('ETH/USDT');
            return $ticker['last'];
        } catch (Exception $e) {
            return 3000; // Fallback aproximado
        }
    }
    
    /**
     * Busca cotação do cache
     */
    private function buscarDoCache($moeda) {
        if (!file_exists($this->cacheFile)) {
            return false;
        }
        
        $cache = json_decode(file_get_contents($this->cacheFile), true);
        if (!$cache || !isset($cache[$moeda])) {
            return false;
        }
        
        $dadosCotacao = $cache[$moeda];
        
        // Verifica se ainda é válido (24 horas)
        if (time() - $dadosCotacao['timestamp'] < $this->cacheValidity) {
            return $dadosCotacao['preco'];
        }
        
        return false;
    }
    
    /**
     * Salva cotação no cache
     */
    private function salvarNoCache($moeda, $preco, $exchange, $par) {
        $cache = [];
        if (file_exists($this->cacheFile)) {
            $cache = json_decode(file_get_contents($this->cacheFile), true) ?: [];
        }
        
        $cache[$moeda] = [
            'preco' => $preco,
            'timestamp' => time(),
            'exchange' => $exchange,
            'par' => $par,
            'data' => date('Y-m-d H:i:s')
        ];
        
        file_put_contents($this->cacheFile, json_encode($cache, JSON_PRETTY_PRINT));
    }
    
    /**
     * Lista todas as exchanges disponíveis
     */
    public function listarExchanges() {
        return array_keys($this->exchanges);
    }
    
    /**
     * Lista todos os pares disponíveis em uma exchange
     */
    public function listarPares($exchangeName) {
        if (!isset($this->exchanges[$exchangeName])) {
            return [];
        }
        
        try {
            $exchange = $this->exchanges[$exchangeName];
            $exchange->load_markets();
            return array_keys($exchange->markets);
        } catch (Exception $e) {
            return [];
        }
    }
}

/**
 * Função compatível com o sistema atual
 * Substitui a função buscarCotacaoReal() existente
 */
function buscarCotacaoRealCCXT($moeda) {
    static $manager = null;
    
    if ($manager === null) {
        $manager = new CCXTCotacaoManager();
    }
    
    return $manager->buscarCotacao($moeda);
}

// Exemplo de uso:
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    // Teste apenas se executado diretamente
    echo "<h2>Teste CCXT Integração</h2>";
    
    $cotacaoManager = new CCXTCotacaoManager();
    
    $moedas = ['BTC', 'ETH', 'BNB', 'SOL', 'USDT', 'ADA'];
    
    foreach ($moedas as $moeda) {
        $preco = $cotacaoManager->buscarCotacao($moeda);
        if ($preco !== false) {
            echo "<p>{$moeda}: $" . number_format($preco, 4) . "</p>";
        } else {
            echo "<p>{$moeda}: Não encontrado</p>";
        }
    }
    
    echo "<h3>Exchanges Disponíveis:</h3>";
    print_r($cotacaoManager->listarExchanges());
} 