<?php
/**
 * Script de Instalação de Dependências - Sistema IN-1888
 * 
 * Este script verifica e instala as dependências necessárias para o suporte a arquivos Excel
 */

echo "<!DOCTYPE html>";
echo "<html><head><title>Instalação de Dependências - IN-1888</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
.container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
.ok { color: green; font-weight: bold; }
.error { color: red; font-weight: bold; }
.warning { color: orange; font-weight: bold; }
.info { color: blue; font-weight: bold; }
.section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
pre { background: #f5f5f5; padding: 10px; overflow-x: auto; border-radius: 3px; }
.command { background: #2d3748; color: #e2e8f0; padding: 10px; border-radius: 5px; font-family: monospace; }
</style></head><body>";

echo "<div class='container'>";
echo "<h1>🚀 Instalação de Dependências - Sistema IN-1888</h1>";
echo "<p>Este script verifica e instala as dependências necessárias para suporte a arquivos Excel (XLSX/XLS).</p>";

// 1. Verificação do Composer
echo "<div class='section'>";
echo "<h2>📦 Verificação do Composer</h2>";

$composerExists = file_exists('composer.json');
$vendorExists = file_exists('vendor/autoload.php');

if ($composerExists) {
    echo "<span class='ok'>✅ composer.json encontrado</span><br>";
} else {
    echo "<span class='error'>❌ composer.json não encontrado</span><br>";
}

if ($vendorExists) {
    echo "<span class='ok'>✅ Dependências já instaladas (vendor/autoload.php existe)</span><br>";
} else {
    echo "<span class='warning'>⚠️ Dependências não instaladas</span><br>";
}

echo "</div>";

// 2. Verificação das Extensões PHP
echo "<div class='section'>";
echo "<h2>🔧 Verificação das Extensões PHP</h2>";

$extensoesNecessarias = [
    'zip' => 'Necessária para ler arquivos XLSX',
    'xml' => 'Necessária para processar XML interno dos arquivos Excel',
    'mbstring' => 'Necessária para manipulação de strings multibyte',
    'gd' => 'Opcional - para processamento de imagens em planilhas'
];

foreach ($extensoesNecessarias as $extensao => $descricao) {
    if (extension_loaded($extensao)) {
        echo "<span class='ok'>✅ $extensao</span> - $descricao<br>";
    } else {
        echo "<span class='error'>❌ $extensao</span> - $descricao<br>";
    }
}

echo "</div>";

// 3. Verificação da Biblioteca PhpSpreadsheet
echo "<div class='section'>";
echo "<h2>📊 Verificação da Biblioteca PhpSpreadsheet</h2>";

if ($vendorExists) {
    try {
        require_once 'vendor/autoload.php';
        
        if (class_exists('\PhpOffice\PhpSpreadsheet\Spreadsheet')) {
            echo "<span class='ok'>✅ PhpSpreadsheet instalada e funcionando</span><br>";
            
            // Testa funcionalidades básicas
            try {
                $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
                $worksheet = $spreadsheet->getActiveSheet();
                $worksheet->setCellValue('A1', 'Teste');
                $value = $worksheet->getCell('A1')->getValue();
                
                if ($value === 'Teste') {
                    echo "<span class='ok'>✅ Teste básico de funcionalidade passou</span><br>";
                } else {
                    echo "<span class='error'>❌ Teste básico de funcionalidade falhou</span><br>";
                }
                
                $spreadsheet->disconnectWorksheets();
                unset($spreadsheet);
                
            } catch (Exception $e) {
                echo "<span class='error'>❌ Erro no teste: " . $e->getMessage() . "</span><br>";
            }
            
        } else {
            echo "<span class='error'>❌ PhpSpreadsheet não encontrada</span><br>";
        }
    } catch (Exception $e) {
        echo "<span class='error'>❌ Erro ao carregar autoload: " . $e->getMessage() . "</span><br>";
    }
} else {
    echo "<span class='warning'>⚠️ Dependências não instaladas - execute composer install</span><br>";
}

echo "</div>";

// 4. Instruções de Instalação
echo "<div class='section'>";
echo "<h2>📋 Instruções de Instalação</h2>";

if (!$vendorExists) {
    echo "<h3>🔧 Para instalar as dependências:</h3>";
    echo "<div class='command'>composer install</div>";
    echo "<p><strong>Ou se não tiver o Composer instalado:</strong></p>";
    echo "<div class='command'>curl -sS https://getcomposer.org/installer | php<br>php composer.phar install</div>";
} else {
    echo "<span class='ok'>✅ Todas as dependências estão instaladas!</span>";
}

echo "<h3>🔧 Para atualizar as dependências:</h3>";
echo "<div class='command'>composer update</div>";

echo "</div>";

// 5. Teste de Arquivo Excel (se existir)
echo "<div class='section'>";
echo "<h2>🧪 Teste com Arquivo Excel</h2>";

$arquivoTeste = 'uploads/BYBIT 01 A 30 DE JUNHO 2025 EXPRESSO BF (1).xls';

if (file_exists($arquivoTeste)) {
    echo "<span class='info'>📄 Arquivo de teste encontrado: " . basename($arquivoTeste) . "</span><br>";
    
    if ($vendorExists && class_exists('\PhpOffice\PhpSpreadsheet\Spreadsheet')) {
        try {
            require_once 'vendor/autoload.php';
            
            $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader(\PhpOffice\PhpSpreadsheet\IOFactory::identify($arquivoTeste));
            $reader->setReadDataOnly(true);
            $spreadsheet = $reader->load($arquivoTeste);
            
            $worksheet = $spreadsheet->getActiveSheet();
            $highestRow = $worksheet->getHighestRow();
            $highestColumn = $worksheet->getHighestColumn();
            
            echo "<span class='ok'>✅ Arquivo lido com sucesso!</span><br>";
            echo "<span class='info'>📊 Dimensões: $highestRow linhas x $highestColumn colunas</span><br>";
            
            // Mostra algumas células de exemplo
            echo "<h4>📋 Primeiras células:</h4>";
            echo "<pre>";
            for ($row = 1; $row <= min(3, $highestRow); $row++) {
                for ($col = 1; $col <= min(5, \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn)); $col++) {
                    $cellValue = $worksheet->getCellByColumnAndRow($col, $row)->getCalculatedValue();
                    echo "[$row,$col]: " . (string)$cellValue . " | ";
                }
                echo "\n";
            }
            echo "</pre>";
            
            $spreadsheet->disconnectWorksheets();
            unset($spreadsheet);
            
        } catch (Exception $e) {
            echo "<span class='error'>❌ Erro ao ler arquivo: " . $e->getMessage() . "</span><br>";
        }
    } else {
        echo "<span class='warning'>⚠️ PhpSpreadsheet não disponível para teste</span><br>";
    }
} else {
    echo "<span class='info'>📄 Nenhum arquivo Excel de teste encontrado</span><br>";
}

echo "</div>";

// 6. Status Final
echo "<div class='section'>";
echo "<h2>🎯 Status Final</h2>";

$tudoOk = $composerExists && $vendorExists && extension_loaded('zip') && extension_loaded('xml');

if ($tudoOk) {
    echo "<span class='ok'>🎉 Sistema pronto para processar arquivos Excel!</span><br>";
    echo "<p>Você pode agora fazer upload de arquivos .xlsx e .xls no sistema principal.</p>";
} else {
    echo "<span class='error'>❌ Sistema não está pronto</span><br>";
    echo "<p>Execute as instruções acima para instalar as dependências necessárias.</p>";
}

echo "</div>";

echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='index.php' style='background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>← Voltar ao Sistema Principal</a>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
