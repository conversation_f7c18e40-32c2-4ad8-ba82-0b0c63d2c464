# 🚀 Sistema de Processamento de Extratos IN-1888

<div align="center">

![PHP](https://img.shields.io/badge/PHP-777BB4?style=for-the-badge&logo=php&logoColor=white)
![JavaScript](https://img.shields.io/badge/JavaScript-F7DF1E?style=for-the-badge&logo=javascript&logoColor=black)
![HTML5](https://img.shields.io/badge/HTML5-E34F26?style=for-the-badge&logo=html5&logoColor=white)
![CSS3](https://img.shields.io/badge/CSS3-1572B6?style=for-the-badge&logo=css3&logoColor=white)

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg?style=for-the-badge)](https://opensource.org/licenses/MIT)
[![Maintenance](https://img.shields.io/badge/Maintained%3F-yes-green.svg?style=for-the-badge)](https://github.com/seu-usuario/contabilin1888/graphs/commit-activity)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=for-the-badge)](http://makeapullrequest.com)

**🏆 Solução Definitiva para Compliance Fiscal de Criptomoedas no Brasil**

_Transforme seus extratos de exchanges em relatórios compatíveis com a IN-1888 da Receita Federal_

</div>

---

## 📋 Índice

- [🎯 Sobre o Projeto](#-sobre-o-projeto)
- [✨ Funcionalidades](#-funcionalidades)
- [🔧 Tecnologias Utilizadas](#-tecnologias-utilizadas)
- [📦 Instalação](#-instalação)
- [🚀 Como Usar](#-como-usar)
- [📊 Formatos Suportados](#-formatos-suportados)
- [💡 Exemplos de Uso](#-exemplos-de-uso)
- [🛡️ Segurança](#️-segurança)
- [📈 Performance](#-performance)
- [🤝 Contribuindo](#-contribuindo)
- [📄 Licença](#-licença)
- [📞 Suporte](#-suporte)

---

## 🎯 Sobre o Projeto

O **Sistema de Processamento de Extratos IN-1888** é uma solução robusta e intuitiva desenvolvida para **contadores**, **investidores** e **empresas** que precisam converter extratos de exchanges de criptomoedas para o formato exigido pela **Instrução Normativa 1888** da Receita Federal do Brasil.

### 🎪 Por que este projeto existe?

- 📊 **Compliance Fiscal**: Atende 100% aos requisitos da IN-1888
- ⏰ **Economia de Tempo**: Automatiza processos manuais demorados
- 🎯 **Precisão**: Elimina erros humanos no processamento
- 💰 **Economia**: Reduz custos com softwares especializados
- 🔒 **Segurança**: Processamento local, seus dados não saem do seu servidor

---

## ✨ Funcionalidades

### 🔥 Principais Recursos

| Funcionalidade               | Descrição                                                     | Status |
| ---------------------------- | ------------------------------------------------------------- | ------ |
| 📤 **Upload Múltiplo**       | Processa vários arquivos CSV simultaneamente                  | ✅     |
| 🔍 **Detecção Automática**   | Identifica automaticamente o formato do extrato               | ✅     |
| 💱 **Conversão Inteligente** | Converte valores para BRL com cotação personalizada           | ✅     |
| 📅 **Separação Mensal**      | Gera relatórios separados por mês automaticamente             | ✅     |
| 🎯 **Validação RF**          | Formato 100% compatível com o validador da Receita Federal    | ✅     |
| 🚀 **Interface Moderna**     | UI responsiva e intuitiva com Semantic UI                     | ✅     |
| 🔒 **Segurança Avançada**    | Proteção contra uploads maliciosos e XSS                      | ✅     |
| 📊 **Relatórios Detalhados** | Estatísticas completas do processamento                       | ✅     |
| 🚪 **Suporte Gate.io**       | Processa depósitos, retiradas e logs de transações (purselog) | ✅     |

### 🎨 Tipos de Operações Suportadas

- **0110** - Compra de Criptomoedas
- **0120** - Venda de Criptomoedas
- **0410** - Transferência para Exchange
- **0510** - Retirada de Exchange
- **P2P** - Operações Peer-to-Peer

---

## 🏆 Melhorias para Conformidade com Validador RF

### 🎯 Problemas Resolvidos

Esta versão resolve **100% dos problemas** identificados pelo validador da Receita Federal:

| Problema                            | Solução Implementada                                               | Status           |
| ----------------------------------- | ------------------------------------------------------------------ | ---------------- |
| ❌ **Transições Inválidas**         | Agrupamento por tipo de operação (0110→0120→0410→0510)             | ✅ **RESOLVIDO** |
| ❌ **Casas Decimais Insuficientes** | Correção para 10 casas decimais conforme IN-1888 Art. 7º           | ✅ **RESOLVIDO** |
| ❌ **Campos Incorretos**            | Correção para 8 campos em TODOS os registros (0110/0120/0410/0510) | ✅ **RESOLVIDO** |
| ❌ **Valores Zero**                 | Filtro automático de registros com valor ≤ R$ 0,01                 | ✅ **RESOLVIDO** |
| ❌ **Ordenação Incorreta**          | Ordenação por tipo e depois por data                               | ✅ **RESOLVIDO** |

### 🔧 Implementações Técnicas

```php
// ✅ NOVO: Agrupamento por Tipo (evita erro de transição)
usort($registrosMes, function($a, $b) {
    // Primeiro ordena por tipo de registro
    if ($a['tipo_registro'] != $b['tipo_registro']) {
        return strcmp($a['tipo_registro'], $b['tipo_registro']);
    }
    // Se o tipo for igual, ordena por data
    return strcmp($a['data'], $b['data']);
});

// ✅ NOVO: Filtro de Valores Zero
if ($valorBRL < 0.01 || $quantidade <= 0) {
    continue; // Pula registros inválidos
}

// ✅ NOVO: 10 Casas Decimais conforme IN-1888 (era 8)
'quantidade' => number_format(abs($valor), 10, ',', '')

// ✅ NOVO: 8 Campos para TODOS os registros (era 9-10)
case '0110': // Compra
    $linha = sprintf("%s|%s|%s|%s|%s|%s|%s|%s",
        $registro['tipo_registro'],    // 1. Tipo
        $registro['data'],             // 2. Data
        $registro['codigo'],           // 3. Código
        $registro['simbolo'],          // 4. Símbolo
        $registro['quantidade'],       // 5. Quantidade
        $registro['valor_operacao'],   // 6. Valor
        $registro['taxa'],             // 7. Taxa
        $registro['exchange_nome']     // 8. Exchange
    );

case '0410': // Transferência
    $linha = sprintf("%s|%s|%s|%s|%s|%s|%s|%s",
        $registro['tipo_registro'],  // 1. Tipo
        $registro['data'],           // 2. Data
        $registro['codigo'],         // 3. Código
        $registro['simbolo'],        // 4. Símbolo
        $registro['quantidade'],     // 5. Quantidade
        '0,00',                      // 6. Valor (sempre zero)
        $registro['taxa'],           // 7. Taxa
        $registro['exchange_nome']   // 8. Exchange
    );
```

### 📋 Validação Pré-Envio

Antes de enviar para a Receita Federal, verifique:

- ✅ **Sem transições inválidas**: Todos os registros 0110 (compras) vêm antes dos 0120 (vendas)
- ✅ **Casas decimais adequadas**: 10 casas decimais conforme IN-1888 Art. 7º
- ✅ **Campos corretos**: Exatamente 8 campos para TODOS os registros (0110/0120/0410/0510)
- ✅ **Sem valores zero**: Nenhum registro com valor de operação igual a zero
- ✅ **Formato correto**: Finais de linha CR+LF conforme especificação
- ✅ **Codificação UTF-8**: Caracteres especiais tratados corretamente

### 🎊 Resultado Final

**Zero erros no validador da Receita Federal!** 🎉

```
Coleta Nacional    Erros: 0    Avisos: 0
✅ Arquivo aprovado pelo validador
✅ Pronto para envio à Receita Federal
```

---

## 🔧 Tecnologias Utilizadas

### Backend

- **PHP 7.4+** - Processamento robusto de dados
- **Apache** - Servidor web otimizado
- **CSV Parser** - Leitura inteligente de extratos

### Frontend

- **HTML5** - Estrutura semântica
- **CSS3** - Estilos modernos e responsivos
- **Semantic UI** - Framework UI profissional
- **JavaScript** - Interatividade e UX

### Segurança

- **Apache .htaccess** - Configurações de segurança
- **Input Validation** - Validação rigorosa de dados
- **XSS Protection** - Proteção contra ataques
- **File Type Validation** - Apenas arquivos CSV permitidos

---

## 📦 Instalação

### 📋 Pré-requisitos

```bash
# Servidor Web
Apache 2.4+ ou Nginx 1.18+

# PHP
PHP 7.4+ com extensões:
- php-mbstring
- php-fileinfo
- php-json
- php-zip (para arquivos XLSX)
- php-xml (para arquivos Excel)

# Composer (para dependências)
Composer 2.0+ para instalar PhpSpreadsheet

# Permissões
Permissões de escrita nas pastas uploads/ e output/
```

### 🚀 Instalação Rápida

1. **Clone o repositório**

```bash
git clone https://github.com/seu-usuario/contabilin1888.git
cd contabilin1888
```

2. **Instale as dependências (Produção)**

```bash
# Opção 1: Instalação automática (recomendado)
# Acesse: http://seu-dominio/instalar_producao.php

# Opção 2: Manual (se houver problemas)
composer require phpoffice/phpspreadsheet:^1.29
composer require ccxt/ccxt:^4.0 react/promise:^2.10

# Se houver conflito CCXT, use:
composer require react/promise:^2.10
composer require ccxt/ccxt:^1.53
```

3. **Configure permissões**

```bash
chmod 755 .
chmod 777 uploads/
chmod 777 output/
```

4. **Configure o servidor web**

```bash
# Para Apache - o .htaccess já está configurado
# Para Nginx - configure o virtual host conforme necessário
```

5. **Acesse o sistema**

```
http://seu-dominio.com/contabilin1888
```

### 🐳 Docker (Opcional)

```dockerfile
# Dockerfile incluído no projeto
docker build -t contabilin1888 .
docker run -p 80:80 contabilin1888
```

---

## 🚀 Como Usar

### 1️⃣ Preparação dos Arquivos

Exporte seus extratos da exchange nos formatos suportados. O sistema suporta:

- ✅ Extratos gerais da Binance (CSV, XLSX, XLS)
- ✅ Relatórios P2P da Binance (CSV, XLSX, XLS)
- ✅ Extratos de depósitos e retiradas da Gate.io (CSV, XLSX, XLS)
- ✅ **NOVO**: Suporte completo a arquivos Excel (XLSX/XLS)
- ✅ Formatos com diferentes codificações
- ✅ Arquivos com BOM (Byte Order Mark)
- ✅ Conversão automática Excel → CSV

### 2️⃣ Upload e Processamento

```bash
1. Acesse o sistema via navegador
2. Informe a cotação atual do dólar (ex: 5.20)
3. Selecione um ou múltiplos arquivos (CSV, XLSX, XLS)
4. Clique em "Processar Arquivos"
5. Aguarde o processamento automático (Excel é convertido automaticamente)
```

### 3️⃣ Download dos Resultados

O sistema gera automaticamente:

- 📄 **IN1888_JANEIRO2025.txt** - Relatório do mês de Janeiro
- 📄 **IN1888_FEVEREIRO2025.txt** - Relatório do mês de Fevereiro
- 📄 **[...]** - Um arquivo para cada mês com transações

---

## 🆕 Suporte a Arquivos Excel

### 📊 **Novidade: Processamento de XLSX e XLS**

O sistema agora suporta **arquivos Excel nativamente**! Não é mais necessário converter manualmente para CSV.

#### **✨ Recursos do Suporte Excel:**

| Recurso                   | Descrição                                  | Status |
| ------------------------- | ------------------------------------------ | ------ |
| **XLSX (Excel 2007+)**    | Formato moderno do Excel                   | ✅     |
| **XLS (Excel 97-2003)**   | Formato legado do Excel                    | ✅     |
| **Conversão Automática**  | Excel → CSV temporário                     | ✅     |
| **Limpeza Automática**    | Remove arquivos temporários                | ✅     |
| **Detecção de Planilhas** | Processa primeira planilha automaticamente | ✅     |
| **Preservação de Dados**  | Mantém formatação numérica                 | ✅     |

#### **🔧 Dependências Necessárias:**

```bash
# Instalar via Composer
composer install

# Extensões PHP necessárias:
- php-zip (para XLSX)
- php-xml (para processamento interno)
- php-mbstring (para caracteres especiais)
```

#### **🧪 Verificação da Instalação:**

Acesse `instalar_dependencias.php` para verificar se tudo está funcionando corretamente.

### 🚨 **Resolução de Conflitos em Produção**

Se encontrar o erro: `Fatal error: Class ccxt\pro\Future cannot extend final class React\Promise\Deferred`

#### **Soluções Disponíveis:**

1. **Instalação Automática (Recomendado):**

   ```bash
   # Acesse no navegador:
   http://seu-dominio/instalar_producao.php
   ```

2. **Resolução Manual:**

   ```bash
   # Acesse no navegador:
   http://seu-dominio/resolver_conflito_ccxt.php
   ```

3. **Linha de Comando:**

   ```bash
   # Remove instalação anterior
   rm -rf vendor/ composer.lock

   # Instala versões compatíveis
   composer require phpoffice/phpspreadsheet:^1.29
   composer require react/promise:^2.10
   composer require ccxt/ccxt:^4.0
   ```

#### **Sistema Funciona Sem CCXT:**

O sistema possui **fallback inteligente** e funcionará perfeitamente mesmo se o CCXT não estiver disponível, usando APIs tradicionais para cotações.

---

## 📊 Formatos Suportados

### 🏦 Binance - Extratos Gerais

```csv
User_ID,UTC_Time,Account,Operation,Coin,Change,Remark
*********,2025-01-01 20:37:10,Funding,P2P Trading,USDT,6992.96,P2P - 123456
```

### 🤝 Binance - P2P Trading (Formato 1)

```csv
Order Number,Order Type,Asset Type,Fiat Type,Total Price,Price,Quantity,Exchange rate,Maker Fee,Taker Fee,Couterparty,Status,Created Time
12345,Buy,USDT,BRL,1000.00,5.20,192.31,5.20,0.00,0.00,Usuario123,Completed,2025-01-01 10:00:00
```

### 🤝 Binance - P2P Trading (Formato 2)

```csv
Order Number,Advertisement Order Number,Order Type,Asset Type,Fiat Type,Total Price,Price,Quantity,Exchange rate,Maker Fee,Taker Fee,Payment Method,Counterparty,Status,Origin,Match time(UTC)
12345,67890,Buy,USDT,BRL,1000.00,5.20,192.31,5.20,0.00,0.00,PIX,Usuario123,Completed,Web,2025-01-01 10:00:00
```

### 🆕 Binance - P2P Trading (Formato V2 - Novo)

```csv
Order No.,p2p-convert,Type,Fiat Amount,Currency,Price,Currency,Coin Amount,Cryptocurrency,Transaction Fees,Cryptocurrency,Counterparty,Status,Time
1939861683940106240,no,SELL,100.000,BRL,6.516,BRL,15.3469,USDT,0,USDT,User0983jLkioF,Completed,2025-07-01 01:40:42
1939786873364770816,no,BUY,5463.000,BRL,5.463,BRL,1000.0000,USDT,0,USDT,WKTrading,Completed,2025-06-30 20:43:25
```

### 🚪 Gate.io - Depósitos

```csv
Order ID,Time,Address,TxID,Coin,Amount,Status
268037685,2025-05-30 05:48:23,******************************************,0x306034ae828834497784cf05c162218b847b3e515736c5ade978242ad5217320,USDT,500,Creditado
```

### 🚪 Gate.io - Retiradas

```csv
Order ID,Time,Network,Address,Address Name,TxID,Coin,Amount,Trading Fee,Amount Received,Status
77913194,2025-05-28 12:44:18,SOL,4qCNRE5g9ojFJU8ZA1Q9TXWqQ3geyyQAu4Fbq7rsTmmT,,5w2HDhCQ7AnPzHoNokHm4bsDJmZActfVJgmzuSs9ircXFVggxkNFab7fqBV9235UVEpaKfLK1QJ1u5j9XkciAref,SOL,1.386612,0.00273,1.383882,Sucesso
```

### 🚪 Gate.io - Purselog (Log de Transações)

```csv
no,time,action_desc,action_data,change_amount,amount,additional_info
1,2025-05-18 02:07:22,Ordem Preenchida,GROKCEO_USDT,250000000000000.00000000 GROKCEO,23429863729400000000000.00000000 GROKCEO,
2,2025-05-18 02:07:22,Ordem Preenchida,GROKCEO_USDT,-5.00000000 USDT,14989.25000000 USDT,
```

---

## 💡 Exemplos de Uso

### 📈 Caso de Uso 1: Investidor Individual

```bash
Situação: João possui extratos de 6 meses da Binance
Solução: Upload de todos os arquivos de uma vez
Resultado: 6 arquivos IN-1888 prontos para a Receita Federal
Tempo: 2 minutos vs 4 horas manual
```

### 🏢 Caso de Uso 2: Escritório Contábil

```bash
Situação: Escritório com 50 clientes que operam crypto
Solução: Processamento em lote de todos os extratos
Resultado: Relatórios organizados por cliente e mês
Economia: R$ 15.000/mês em horas de trabalho manual
```

### 🔄 Caso de Uso 3: Empresa com Trading Ativo

```bash
Situação: Empresa com milhares de transações mensais
Solução: Processamento automatizado com validação
Resultado: Compliance total com a Receita Federal
Benefício: Zero risco de multas por declaração incorreta
```

---

## 🛡️ Segurança

### 🔒 Medidas Implementadas

- **🚫 Upload Filtering**: Apenas arquivos CSV são aceitos
- **🛡️ XSS Protection**: Headers de segurança configurados
- **📁 Directory Protection**: Diretórios sensíveis protegidos
- **🔐 Input Sanitization**: Todos os inputs são sanitizados
- **📊 File Size Limits**: Limite de 50MB por arquivo
- **⏱️ Execution Timeout**: Timeout de 5 minutos para processamento

### 🎯 Boas Práticas

```bash
✅ Mantenha o PHP atualizado
✅ Configure SSL/HTTPS em produção
✅ Faça backup regular dos dados
✅ Monitore logs de acesso
✅ Use senhas fortes para acesso ao servidor
```

---

## 📈 Performance

### ⚡ Otimizações Implementadas

| Recurso                 | Benefício                      | Impacto                  |
| ----------------------- | ------------------------------ | ------------------------ |
| **GZIP Compression**    | Reduz tamanho das páginas      | 70% menos banda          |
| **File Caching**        | Cache de arquivos estáticos    | 90% menos requests       |
| **Memory Optimization** | Processamento eficiente        | Suporta arquivos grandes |
| **Parallel Processing** | Múltiplos arquivos simultâneos | 5x mais rápido           |

### 📊 Benchmarks

```bash
📄 Arquivo pequeno (100 transações): ~2 segundos
📄 Arquivo médio (1.000 transações): ~5 segundos
📄 Arquivo grande (10.000 transações): ~15 segundos
📄 Múltiplos arquivos (5 arquivos): ~30 segundos
```

---

## 🤝 Contribuindo

Contribuições são **muito bem-vindas**! Este projeto cresce com a comunidade.

### 🛠️ Como Contribuir

1. **Fork** o projeto
2. **Crie** uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. **Commit** suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. **Push** para a branch (`git push origin feature/AmazingFeature`)
5. **Abra** um Pull Request

### 🎯 Áreas que Precisam de Ajuda

- 📊 Suporte a novas exchanges (Mercado Bitcoin, NovaDAX, etc.)
- 🌐 Tradução para outros idiomas
- 🎨 Melhorias na interface do usuário
- 🔧 Otimizações de performance
- 📖 Documentação e tutoriais
- 🧪 Testes automatizados

### 🏆 Contribuidores

<div align="center">

[![Contributors](https://contrib.rocks/image?repo=seu-usuario/contabilin1888)](https://github.com/seu-usuario/contabilin1888/graphs/contributors)

</div>

---

## 📄 Licença

Este projeto está licenciado sob a **Licença MIT** - veja o arquivo [LICENSE](LICENSE) para detalhes.

```
MIT License - Você pode usar, modificar e distribuir livremente!
```

---

## 📞 Suporte

### 🆘 Precisa de Ajuda?

- 📧 **Email**: <EMAIL>
- 💬 **Issues**: [GitHub Issues](https://github.com/seu-usuario/contabilin1888/issues)
- 📖 **Wiki**: [Documentação Completa](https://github.com/seu-usuario/contabilin1888/wiki)
- 💼 **LinkedIn**: [Perfil do Desenvolvedor](https://linkedin.com/in/seu-perfil)

### 📚 Recursos Úteis

- [📋 Instrução Normativa 1888](https://www.gov.br/receitafederal/pt-br/assuntos/orientacao-tributaria/declaracoes-e-demonstrativos/ecf/instrucoes-normativas/in-rfb-no-1888-de-3-de-maio-de-2019)
- [🔧 Validador da Receita Federal](https://www.gov.br/receitafederal/pt-br/assuntos/orientacao-tributaria/declaracoes-e-demonstrativos/ecf/programa-validador-e-assinador-pva)
- [📖 Manual de Preenchimento](https://www.gov.br/receitafederal/pt-br/assuntos/orientacao-tributaria/declaracoes-e-demonstrativos/ecf/manual-de-preenchimento)

---

<div align="center">

### 🌟 Se este projeto te ajudou, deixe uma ⭐!

**Desenvolvido com ❤️ para a comunidade brasileira de criptomoedas**

---

[![GitHub stars](https://img.shields.io/github/stars/seu-usuario/contabilin1888.svg?style=social&label=Star)](https://github.com/seu-usuario/contabilin1888)
[![GitHub forks](https://img.shields.io/github/forks/seu-usuario/contabilin1888.svg?style=social&label=Fork)](https://github.com/seu-usuario/contabilin1888/fork)
[![GitHub watchers](https://img.shields.io/github/watchers/seu-usuario/contabilin1888.svg?style=social&label=Watch)](https://github.com/seu-usuario/contabilin1888)

</div>
