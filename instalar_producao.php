<?php
/**
 * Script de Instalação para Produção - Sistema IN-1888
 * 
 * Instala dependências de forma segura, resolvendo conflitos conhecidos
 */

echo "<!DOCTYPE html>";
echo "<html><head><title>Instalação Produção - IN-1888</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
.container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
.ok { color: green; font-weight: bold; }
.error { color: red; font-weight: bold; }
.warning { color: orange; font-weight: bold; }
.info { color: blue; font-weight: bold; }
.section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
.command { background: #2d3748; color: #e2e8f0; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; }
pre { background: #f5f5f5; padding: 10px; overflow-x: auto; border-radius: 3px; }
.step { background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #4CAF50; }
.progress { width: 100%; background-color: #f0f0f0; border-radius: 10px; margin: 10px 0; }
.progress-bar { height: 20px; background-color: #4CAF50; border-radius: 10px; text-align: center; line-height: 20px; color: white; }
</style></head><body>";

echo "<div class='container'>";
echo "<h1>🚀 Instalação para Produção - Sistema IN-1888</h1>";
echo "<p>Este script instala as dependências de forma segura, evitando conflitos conhecidos.</p>";

// Função para executar comandos e mostrar progresso
function executarComando($comando, $descricao) {
    echo "<div class='step'>";
    echo "<h3>🔧 $descricao</h3>";
    echo "<div class='command'>$comando</div>";
    
    $output = [];
    $returnCode = 0;
    
    // Executa comando
    exec($comando . ' 2>&1', $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "<span class='ok'>✅ Sucesso!</span><br>";
        if (!empty($output)) {
            echo "<pre>" . implode("\n", array_slice($output, -10)) . "</pre>"; // Últimas 10 linhas
        }
    } else {
        echo "<span class='error'>❌ Erro (código: $returnCode)</span><br>";
        echo "<pre>" . implode("\n", $output) . "</pre>";
    }
    
    echo "</div>";
    return $returnCode === 0;
}

// Etapa 1: Verificação do ambiente
echo "<div class='section'>";
echo "<h2>🔍 Verificação do Ambiente</h2>";

$phpVersion = phpversion();
$composerExists = shell_exec('composer --version 2>/dev/null');

echo "<table border='1' style='width:100%; border-collapse: collapse;'>";
echo "<tr><th>Componente</th><th>Status</th><th>Detalhes</th></tr>";

// PHP
if (version_compare($phpVersion, '7.4.0', '>=')) {
    echo "<tr><td>PHP</td><td><span class='ok'>✅ OK</span></td><td>$phpVersion</td></tr>";
} else {
    echo "<tr><td>PHP</td><td><span class='error'>❌ Versão baixa</span></td><td>$phpVersion (mínimo: 7.4)</td></tr>";
}

// Composer
if ($composerExists) {
    echo "<tr><td>Composer</td><td><span class='ok'>✅ OK</span></td><td>" . trim($composerExists) . "</td></tr>";
} else {
    echo "<tr><td>Composer</td><td><span class='error'>❌ Não encontrado</span></td><td>Instale o Composer</td></tr>";
}

// Extensões PHP necessárias
$extensoes = ['zip', 'xml', 'mbstring', 'json', 'curl'];
foreach ($extensoes as $ext) {
    if (extension_loaded($ext)) {
        echo "<tr><td>php-$ext</td><td><span class='ok'>✅ OK</span></td><td>Carregada</td></tr>";
    } else {
        echo "<tr><td>php-$ext</td><td><span class='error'>❌ Faltando</span></td><td>Instale php-$ext</td></tr>";
    }
}

echo "</table>";
echo "</div>";

// Etapa 2: Limpeza (se necessário)
if (isset($_GET['action']) && $_GET['action'] === 'install') {
    
    echo "<div class='section'>";
    echo "<h2>🗑️ Limpeza de Instalação Anterior</h2>";
    
    if (is_dir('vendor')) {
        echo "<span class='info'>🗑️ Removendo vendor/ anterior...</span><br>";
        // Remove vendor directory
        function removeDir($dir) {
            if (is_dir($dir)) {
                $objects = scandir($dir);
                foreach ($objects as $object) {
                    if ($object != "." && $object != "..") {
                        if (is_dir($dir . "/" . $object)) {
                            removeDir($dir . "/" . $object);
                        } else {
                            unlink($dir . "/" . $object);
                        }
                    }
                }
                rmdir($dir);
            }
        }
        removeDir('vendor');
        echo "<span class='ok'>✅ vendor/ removido</span><br>";
    }
    
    if (file_exists('composer.lock')) {
        unlink('composer.lock');
        echo "<span class='ok'>✅ composer.lock removido</span><br>";
    }
    
    echo "</div>";
    
    // Etapa 3: Instalação das dependências
    echo "<div class='section'>";
    echo "<h2>📦 Instalação das Dependências</h2>";
    
    // Progresso
    echo "<div class='progress'><div class='progress-bar' style='width: 10%;'>10%</div></div>";
    
    // Instala PhpSpreadsheet primeiro (mais estável)
    $sucesso1 = executarComando('composer require phpoffice/phpspreadsheet:^1.29 --no-interaction', 'Instalando PhpSpreadsheet');
    
    echo "<div class='progress'><div class='progress-bar' style='width: 50%;'>50%</div></div>";
    
    // Tenta instalar CCXT com versão compatível
    $sucesso2 = executarComando('composer require ccxt/ccxt:^4.0 react/promise:^2.10 --no-interaction', 'Instalando CCXT v4 (compatível)');
    
    if (!$sucesso2) {
        echo "<div class='warning'>";
        echo "<h3>⚠️ CCXT falhou - tentando versão alternativa</h3>";
        $sucesso2 = executarComando('composer require react/promise:^2.10 --no-interaction', 'Forçando React/Promise v2.10');
        
        if ($sucesso2) {
            $sucesso2 = executarComando('composer require ccxt/ccxt:^1.53 --no-interaction', 'Instalando CCXT v1.53');
        }
        echo "</div>";
    }
    
    echo "<div class='progress'><div class='progress-bar' style='width: 80%;'>80%</div></div>";
    
    // Otimiza autoloader
    executarComando('composer dump-autoload --optimize', 'Otimizando autoloader');
    
    echo "<div class='progress'><div class='progress-bar' style='width: 100%;'>100% - Concluído!</div></div>";
    
    echo "</div>";
    
    // Etapa 4: Teste das dependências
    echo "<div class='section'>";
    echo "<h2>🧪 Teste das Dependências</h2>";
    
    if (file_exists('vendor/autoload.php')) {
        echo "<span class='ok'>✅ Autoload disponível</span><br>";
        
        try {
            require_once 'vendor/autoload.php';
            
            // Testa PhpSpreadsheet
            if (class_exists('\PhpOffice\PhpSpreadsheet\Spreadsheet')) {
                echo "<span class='ok'>✅ PhpSpreadsheet funcionando</span><br>";
                
                // Teste básico
                $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
                $worksheet = $spreadsheet->getActiveSheet();
                $worksheet->setCellValue('A1', 'Teste');
                $value = $worksheet->getCell('A1')->getValue();
                
                if ($value === 'Teste') {
                    echo "<span class='ok'>✅ PhpSpreadsheet teste básico passou</span><br>";
                }
                
                $spreadsheet->disconnectWorksheets();
                unset($spreadsheet);
                
            } else {
                echo "<span class='error'>❌ PhpSpreadsheet não encontrado</span><br>";
            }
            
            // Testa CCXT (com proteção contra conflitos)
            try {
                if (class_exists('\ccxt\Exchange')) {
                    echo "<span class='ok'>✅ CCXT carregado</span><br>";
                    
                    // Teste básico (sem fazer requisições reais)
                    $exchange = new \ccxt\binance(['sandbox' => true]);
                    echo "<span class='ok'>✅ CCXT Exchange criada sem erros</span><br>";
                    
                } else {
                    echo "<span class='warning'>⚠️ CCXT não disponível (sistema funcionará com APIs tradicionais)</span><br>";
                }
            } catch (Error $e) {
                echo "<span class='error'>❌ Conflito CCXT detectado: " . $e->getMessage() . "</span><br>";
                echo "<span class='info'>ℹ️ Sistema funcionará sem CCXT (usando APIs tradicionais)</span><br>";
            }
            
        } catch (Exception $e) {
            echo "<span class='error'>❌ Erro ao carregar dependências: " . $e->getMessage() . "</span><br>";
        }
        
    } else {
        echo "<span class='error'>❌ Autoload não encontrado</span><br>";
    }
    
    echo "</div>";
    
    // Etapa 5: Configuração final
    echo "<div class='section'>";
    echo "<h2>⚙️ Configuração Final</h2>";
    
    // Verifica permissões
    $dirs = ['uploads', 'output'];
    foreach ($dirs as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0777, true);
            echo "<span class='ok'>✅ Diretório $dir/ criado</span><br>";
        }
        
        if (is_writable($dir)) {
            echo "<span class='ok'>✅ $dir/ tem permissão de escrita</span><br>";
        } else {
            echo "<span class='warning'>⚠️ $dir/ sem permissão de escrita - execute: chmod 777 $dir/</span><br>";
        }
    }
    
    echo "</div>";
    
    // Resultado final
    echo "<div class='section'>";
    echo "<h2>🎯 Resultado da Instalação</h2>";
    
    $phpSpreadsheetOk = class_exists('\PhpOffice\PhpSpreadsheet\Spreadsheet');
    $permissoesOk = is_writable('uploads') && is_writable('output');
    
    if ($phpSpreadsheetOk && $permissoesOk) {
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; border: 1px solid #c3e6cb;'>";
        echo "<h3 style='color: #155724;'>🎉 Instalação Concluída com Sucesso!</h3>";
        echo "<p>O sistema está pronto para uso em produção.</p>";
        echo "<ul>";
        echo "<li>✅ PhpSpreadsheet instalado (suporte a Excel)</li>";
        echo "<li>✅ Permissões configuradas</li>";
        echo "<li>✅ Sistema funcionará mesmo se CCXT tiver conflitos</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div style='text-align: center; margin-top: 20px;'>";
        echo "<a href='index.php' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 18px;'>🚀 Acessar Sistema</a>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 5px; border: 1px solid #f5c6cb;'>";
        echo "<h3 style='color: #721c24;'>❌ Instalação Incompleta</h3>";
        echo "<p>Alguns problemas precisam ser resolvidos:</p>";
        echo "<ul>";
        if (!$phpSpreadsheetOk) echo "<li>❌ PhpSpreadsheet não funcionando</li>";
        if (!$permissoesOk) echo "<li>❌ Permissões de diretório</li>";
        echo "</ul>";
        echo "</div>";
    }
    
    echo "</div>";
    
} else {
    // Mostra botão para iniciar instalação
    echo "<div class='section'>";
    echo "<h2>🚀 Iniciar Instalação</h2>";
    echo "<p>Clique no botão abaixo para iniciar a instalação automática das dependências.</p>";
    
    echo "<div style='text-align: center;'>";
    echo "<a href='?action=install' style='background: #007bff; color: white; padding: 20px 40px; text-decoration: none; border-radius: 5px; font-size: 20px;'>🚀 Instalar Dependências</a>";
    echo "</div>";
    
    echo "<div style='margin-top: 20px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;'>";
    echo "<h3>⚠️ O que será feito:</h3>";
    echo "<ul>";
    echo "<li>Limpeza de instalações anteriores</li>";
    echo "<li>Instalação do PhpSpreadsheet (suporte Excel)</li>";
    echo "<li>Instalação do CCXT com versões compatíveis</li>";
    echo "<li>Configuração de permissões</li>";
    echo "<li>Testes de funcionamento</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
}

echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='resolver_conflito_ccxt.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔧 Resolver Conflitos</a>";
echo "<a href='instalar_dependencias.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📋 Verificar Status</a>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
