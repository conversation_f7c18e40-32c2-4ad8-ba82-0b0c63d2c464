{"mcpServers": {"ccxt-crypto": {"command": "uvx", "args": ["--from", "ccxt-mcp", "ccxt-mcp"], "env": {"BINANCE_API_KEY": "sua_api_key_binance", "BINANCE_SECRET": "seu_secret_binance", "GATE_API_KEY": "sua_api_key_gate", "GATE_SECRET": "seu_secret_gate"}}, "coinmarketcap": {"command": "uvx", "args": ["--from", "coinmarketcap-mcp", "coinmarketcap-mcp"], "env": {"CMC_API_KEY": "sua_api_key_coinmarketcap"}}, "alpha-vantage": {"command": "uvx", "args": ["--from", "alpha-vantage-mcp", "alpha-vantage-mcp"], "env": {"ALPHA_VANTAGE_API_KEY": "sua_api_key_alpha_vantage"}}}}