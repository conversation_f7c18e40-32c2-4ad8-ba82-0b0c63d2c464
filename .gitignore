# ========================================
# 🚀 CONTABILIN1888 - GITIGNORE
# Sistema de Processamento de Extratos IN-1888
# ========================================

# ==========================================
# 📁 DADOS SENSÍVEIS E ARQUIVOS DE USUÁRIO
# ==========================================

# Arquivos de uploads (CSVs enviados pelos usuários - DADOS SENSÍVEIS)
uploads/*
!uploads/.gitkeep

# Arquivos de saída gerados (relatórios IN-1888 - DADOS SENSÍVEIS)
output/*
!output/.gitkeep

# Cache de cotações (pode conter dados de API)
cache_cotacoes.json
cache_cotacoes_*.json

# ==========================================
# 🧪 ARQUIVOS DE DESENVOLVIMENTO E TESTE
# ==========================================

# Arquivos de teste e debug
teste_*.php
debug_*.php
temp_*.php
test_*.php
*_test.php
diagnostico.php
reprocessar_*.php
gerar_arquivo_*.php

# Arquivos de validação específicos
teste_validacao_*.php
*_validacao.php

# ==========================================
# 📦 DEPENDÊNCIAS E VENDOR
# ==========================================

# Composer
vendor/
composer.lock
composer.phar

# Node.js (caso seja usado no futuro)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json

# ==========================================
# 🗂️ ARQUIVOS TEMPORÁRIOS E CACHE
# ==========================================

# Arquivos temporários PHP
*.tmp
*.temp
*.cache

# Arquivos de sessão PHP
sess_*
session_*

# Cache de aplicação
cache/
tmp/
temp/

# ==========================================
# 📋 LOGS E MONITORAMENTO
# ==========================================

# Arquivos de log
*.log
log.txt
logs/
error_log
php_errors.log
access.log
debug.log

# ==========================================
# 💾 BACKUPS E ARQUIVOS ANTIGOS
# ==========================================

# Arquivos de backup
*.bak
*.backup
*.old
*~
*.orig

# Arquivos compactados
*.zip
*.rar
*.tar.gz
*.7z

# ==========================================
# 🖥️ SISTEMA OPERACIONAL
# ==========================================

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
.AppleDouble
.LSOverride

# Linux
*~
.directory
.Trash-*

# ==========================================
# 🛠️ EDITORES E IDEs
# ==========================================

# Visual Studio Code
.vscode/
*.code-workspace

# PhpStorm / IntelliJ
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-*
.sublime-project
.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc

# ==========================================
# 🔧 CONFIGURAÇÕES LOCAIS
# ==========================================

# Configurações de ambiente
.env
.env.local
.env.development
.env.production
config.local.php
local_config.php

# Configurações específicas do servidor
.htaccess.local
php.ini.local

# ==========================================
# 📄 DOCUMENTAÇÃO
# ==========================================

# Todos os arquivos Markdown exceto README.md (concentrar info no README)
*.md
!README.md

# Rascunhos e notas
*_rascunho.*
*_draft.*
notas_*.*
TODO_*.*

# ==========================================
# 🔒 SEGURANÇA
# ==========================================

# Chaves e certificados
*.key
*.pem
*.crt
*.p12
*.pfx

# Arquivos de configuração com credenciais
credentials.json
secrets.json
api_keys.php

# ==========================================
# 📊 ARQUIVOS ESPECÍFICOS DO PROJETO
# ==========================================

# Manuais em PDF (muito grandes para Git)
*.pdf

# Arquivos JSON muito grandes
manual_criptoativos_estruturado_*.json
manual_criptoativos_estruturado_*.html

# Configurações MCP específicas (podem conter dados sensíveis)
mcp_config.json
mcp_config_*.json
!mcp_config_example.json