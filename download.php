<?php
/**
 * Script de Download Seguro para Arquivos IN-1888
 * 
 * Este script gerencia downloads de forma segura e compatível com diferentes servidores
 */

// Configurações de segurança
ini_set('display_errors', 0);
error_reporting(0);

// Definir diretório de saída
define('OUTPUT_DIR', 'output/');

// Função para sanitizar nome do arquivo
function sanitizeFilename($filename) {
    // Remove caracteres perigosos
    $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
    // Remove múltiplos pontos
    $filename = preg_replace('/\.+/', '.', $filename);
    return $filename;
}

// Verifica se o arquivo foi solicitado
if (!isset($_GET['file']) || empty($_GET['file'])) {
    http_response_code(400);
    die('Arquivo não especificado');
}

// Sanitiza o nome do arquivo
$requestedFile = sanitizeFilename($_GET['file']);

// Constrói o caminho completo do arquivo
$filePath = OUTPUT_DIR . $requestedFile;

// Verificações de segurança
if (!file_exists($filePath)) {
    http_response_code(404);
    die('Arquivo não encontrado');
}

// Verifica se é um arquivo válido (apenas .txt)
if (pathinfo($filePath, PATHINFO_EXTENSION) !== 'txt') {
    http_response_code(403);
    die('Tipo de arquivo não permitido');
}

// Verifica se o arquivo está dentro do diretório permitido
$realPath = realpath($filePath);
$allowedDir = realpath(OUTPUT_DIR);

if (strpos($realPath, $allowedDir) !== 0) {
    http_response_code(403);
    die('Acesso negado');
}

// Obtém informações do arquivo
$fileSize = filesize($filePath);
$fileName = basename($filePath);

// Limpa qualquer output anterior
if (ob_get_level()) {
    ob_end_clean();
}

// Define headers para download
header('Content-Type: application/octet-stream');
header('Content-Disposition: attachment; filename="' . $fileName . '"');
header('Content-Length: ' . $fileSize);
header('Cache-Control: no-cache, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Para arquivos grandes, use readfile com buffer
if ($fileSize > 10 * 1024 * 1024) { // 10MB
    $handle = fopen($filePath, 'rb');
    if ($handle) {
        while (!feof($handle)) {
            echo fread($handle, 8192);
            flush();
        }
        fclose($handle);
    }
} else {
    // Para arquivos menores, use readfile diretamente
    readfile($filePath);
}

// Termina a execução
exit;
?> 